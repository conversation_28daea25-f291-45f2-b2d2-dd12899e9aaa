#commentForm {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: start;
  margin-bottom: 20px;
  /* max-width: 600px; */
  background-color: transparent;
  padding: 10px;
  border-radius: 10px;
}

#commentForm .form__avatar {
  position: relative;
  display: flex;
  background-color: #fff;
  background: url(../comm_form/userpic.png) no-repeat center center / cover; /* путь к дефолтной картинке юзера */
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
}

#commentForm .form__avatar img {
  width: 100%;
  max-width: 100%;
}

#commentForm .form__avatar::before {
  position: absolute;
  content: "Select";
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  font-size: 12px;
}

#commentForm .form__avatar--loaded::before {
  display: none;
}

#commentForm label {
  color: #fff;
}

#inputCommentName {
  margin-bottom: 10px;
}

#commentForm input,
#commentForm textarea {
  width: 100%;
  padding: 12px 20px;
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #f8f8f8;
  resize: none;
}

#commentForm input:focus,
#commentForm textarea:focus {
  outline: none;
}

#commentForm .form__inputs {
  position: relative;
  width: 100%;
  margin-right: 50px;
}

#commentPush {
  /* position: absolute; */
  /* height: 40px; */
  background-color: #0866ff;
  border: none;
  border-radius: 5px;
  color: #fff;
  padding: 15px 30px;
  text-decoration: none;
  margin: 15px 0px 0px 0px;
  cursor: pointer;
  font-size: 16px;
  text-transform: uppercase;
}

#commentPush img {
  width: 30px;
}

.form__item {
  /* width: 100%; */
  margin-right: 10px;
  color: snow;
  border-radius: 50%;
}

.form__label {
  font-size: 12px;
}

.file {
  width: 100%;
  max-width: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.file__item {
  position: relative;
}

.file__input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  font-size: 0;
  cursor: pointer;
}

.file__button {
  display: inline-flex;
  min-height: 40px;
  justify-content: center;
  align-items: center;
  background-color: #bf0909;
  color: whitesmoke;
  border-radius: 5px;
  padding: 0px 20px;
}

.file__preview {
  display: inline-block;
  max-width: 100px;
  padding: 10px 0px 0px 0px;
}

.file__preview img {
  max-width: 100%;
}

.error {
  outline: 3px solid #a12000;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn__order {
  display: block;
  padding: 15px 25px;
  text-align: center;
  background: linear-gradient(90deg, #041e37 0, rgba(9, 70, 121, 1) 35%, #5181b0 100%);
  color: #fff;
  font-family: Helvetica, Arial, sans-serif;
  text-transform: uppercase;
  font-weight: 600;
  border-radius: 5px;
  text-decoration: none;
}

.ac_footer {
  position: relative;
  top: 10px;
  height: 0;
  text-align: center;
  margin-bottom: 70px;
  color: rgba(9, 70, 121, 1);
}

.ac_footer a {
  color: rgba(9, 70, 121, 1);
}

.ac_footer p {
  text-align: center;
}

@media (max-width: 480px) {
  #commentForm {
    justify-content: center;
    padding: 0;
  }

  #commentForm .form__avatar {
    width: 60px;
    height: 60px;
  }

  #commentForm .form__avatar::before {
    font-size: 10px;
  }

  #commentForm input,
  #commentForm textarea {
    border-width: 1px;
    padding: 10px 45px 10px 15px;
    border-radius: 5px;
    font-size: 14px;
  }

  #commentPush {
    padding: 5px;
    /* width: 45px; */
    right: 0;
  }

  #commentForm .form__inputs {
    margin: 0;
  }
}
