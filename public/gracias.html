<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G<PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            text-align: center;
            flex-grow: 1;
        }
        h1 {
            color: #2c5b8a; 
        }
        .footer {
            text-align: center;
            padding: 20px;
            background: #333;
            color: #fff;
            margin-top: 30px;
        }
        .footer p {
            margin: 5px 0;
        }
        .footer a {
            color: #a0c6e8;
            text-decoration: none;
            margin: 0 10px;
        }
        .footer a:hover {
            text-decoration: underline;
        }

        /* Modal Styles */
        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1000; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.6); /* Black w/ opacity */
        }
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto; /* 5% from the top and centered */
            padding: 25px;
            border: 1px solid #888;
            width: 80%; /* Could be more or less, depending on screen size */
            max-width: 700px;
            border-radius: 8px;
            position: relative;
        }
        .modal-content h2 {
            margin-top: 0;
            color: #2c5b8a;
        }
        .modal-content ul {
            padding-left: 20px;
        }
        .modal-content li {
            margin-bottom: 10px;
        }
        .close-button {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 10px;
            right: 20px;
        }
        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
    </style>

</head>
<body>
<img height="1" width="1" style="display:none"
src="https://www.facebook.com/tr?id=463877569727309&ev=Lead&noscript=1"
/>

    <div class="container">
        <h1>¡Gracias por tu Pedido!</h1>
        <p>Tu solicitud ha sido recibida con éxito.</p>
        <p>Un representante de nuestro equipo se pondrá en contacto contigo a la brevedad para confirmar los detalles y ayudarte con los siguientes pasos.</p>
        <p>Agradecemos tu confianza.</p>
     
    </div>

    <footer class="footer">
        <p>
            <a href="#" id="openPrivacyModal">Política de Privacidad</a> |
            <a href="#" id="openTermsModal">Términos y Condiciones</a>
        </p>
        <p>
            Enzimlex © <span id="currentYear"></span>. Todos los derechos reservados.
        </p>
        <p>Contacto:</p>
        <p>Correo electrónico: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <p>Teléfono: <a href="tel:+524939328266">+52 493 932 82 66</a></p>
        <p>Dirección: C. Francisco I. Madero 41-41, Centro, 99000 Fresnillo, Zac., México</p>
        <p>
            Síguenos en: <a href="https://www.facebook.com/saludDelMundo" target="_blank" rel="noopener noreferrer">Facebook</a>
        </p>
    </footer>

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="modal">
        <div class="modal-content">
            <span class="close-button" data-modal-close="privacyModal">×</span>
            <h2>Política de Privacidad</h2>
            <p><strong>1. Información que Recopilamos</strong><br>
            En Enzimlex (enzimlex.mirus.help), recopilamos información personal como nombre, dirección de correo electrónico, número de teléfono y, en algunos casos, información sobre sus intereses de bienestar y preferencias de productos cuando usted:</p>
            <ul>
                <li>Se registra en nuestro sitio web</li>
                <li>Completa formularios de contacto o de pedido</li>
                <li>Participa en nuestros programas y promociones</li>
                <li>Se suscribe a nuestro boletín informativo</li>
            </ul>
            <p><strong>2. Uso de la Información</strong><br>
            Utilizamos la información recopilada para:</p>
            <ul>
                <li>Procesar sus pedidos y proporcionar nuestros productos (suplementos alimenticios)</li>
                <li>Mejorar nuestros productos, servicios y la experiencia en nuestro sitio web</li>
                <li>Comunicarnos con usted sobre su cuenta, pedidos y nuestros productos</li>
                <li>Responder a sus consultas y solicitudes</li>
                <li>Enviar información relevante sobre bienestar, nutrición y nuestros suplementos alimenticios (siempre que haya consentido recibirla y en cumplimiento con la normativa aplicable)</li>
                <li>Cumplir con obligaciones legales y regulatorias</li>
            </ul>
            <p><strong>3. Protección de Datos</strong><br>
            Implementamos medidas de seguridad técnicas y organizativas para proteger su información personal contra acceso no autorizado, pérdida o alteración.</p>
            <p><strong>4. Compartir Información</strong><br>
            No vendemos, alquilamos ni compartimos su información personal con terceros con fines de marketing ajenos a nuestra actividad. Podemos compartir información con:</p>
            <ul>
                <li>Proveedores de servicios que nos ayudan a operar nuestro sitio web, procesar pagos y realizar envíos.</li>
                <li>Profesionales de la salud (por ejemplo, nutriólogos o asesores de bienestar) que colaboran con nosotros para ofrecer información general, siempre bajo su consentimiento y con fines informativos, no de diagnóstico o tratamiento.</li>
                <li>Autoridades cuando sea requerido por ley</li>
            </ul>
            <p><strong>5. Sus Derechos</strong><br>
            Usted tiene derecho a:</p>
            <ul>
                <li>Acceder a su información personal</li>
                <li>Rectificar datos inexactos</li>
                <li>Solicitar la eliminación de sus datos</li>
                <li>Oponerse al procesamiento de sus datos</li>
                <li>Retirar su consentimiento en cualquier momento</li>
            </ul>
            <p><strong>6. Cookies y Tecnologías Similares</strong><br>
            Utilizamos cookies y tecnologías similares para mejorar su experiencia en nuestro sitio web, analizar el tráfico y personalizar el contenido y la publicidad. Puede configurar su navegador para rechazar cookies, aunque esto puede afectar la funcionalidad del sitio.</p>
            <p><strong>7. Cambios en la Política de Privacidad</strong><br>
            Podemos actualizar esta política periódicamente. Le notificaremos sobre cambios significativos mediante un aviso en nuestro sitio web o por correo electrónico.</p>
            <p><strong>8. Contacto</strong><br>
            Si tiene preguntas sobre nuestra Política de Privacidad, contáctenos en:</p>
            <p>Correo electrónico: [Tu Correo Electrónico]<br>
            Teléfono: +52 493 932 82 66<br>
            Dirección: <EMAIL></p>
            <p><em>Última actualización: 05.05.2025</em></p>
        </div>
    </div>
    <!-- Terms and Conditions Modal -->
    <div id="termsModal" class="modal">
        <div class="modal-content">
            <span class="close-button" data-modal-close="termsModal">×</span>
            <h2>Términos y Condiciones</h2>
            <p><strong>1. Aceptación de los Términos</strong><br>
            Al acceder y utilizar el sitio web de Enzimlex (enzimlex.mirus.help) ("el Sitio"), usted acepta cumplir con estos Términos y Condiciones y nuestra Política de Privacidad. Si no está de acuerdo con alguna parte de estos términos, le pedimos que no utilice nuestro Sitio.</p>
            <p><strong>2. Uso del Sitio Web</strong><br>
            Usted acepta utilizar nuestro Sitio únicamente con fines legales, para informarse y adquirir nuestros suplementos alimenticios, y de manera que no infrinja los derechos de terceros ni restrinja o impida el uso y disfrute del Sitio por parte de otros.</p>
            <p><strong>3. Información sobre Productos y Descargos de Responsabilidad</strong><br>
            Los productos ofrecidos en este Sitio son <strong>suplementos alimenticios</strong>. <strong>ESTOS PRODUCTOS NO SON MEDICAMENTOS.</strong></p>
            <p>La información proporcionada en este Sitio sobre nuestros productos, bienestar y nutrición tiene fines informativos y educativos generales. <strong>NO PRETENDE SUSTITUIR EL CONSEJO, DIAGNÓSTICO O TRATAMIENTO DE UN PROFESIONAL DE LA SALUD CALIFICADO.</strong></p>
            <p><strong>LOS SUPLEMENTOS ALIMENTICIOS NO ESTÁN DESTINADOS A DIAGNOSTICAR, TRATAR, CURAR O PREVENIR NINGUNA ENFERMEDAD.</strong></p>
            <p>Siempre consulte a su médico u otro profesional de la salud calificado si tiene preguntas sobre una condición médica o antes de comenzar a tomar cualquier suplemento alimenticio, especialmente si está embarazada, en período de lactancia, tomando medicamentos o tiene una condición médica preexistente. No ignore ni retrase la búsqueda de consejo médico profesional debido a algo que haya leído en este Sitio.</p>
            <p>El consumo de los productos ofrecidos es responsabilidad de quien los recomienda y de quien los usa. Los resultados individuales pueden variar.</p>
            
            <p><strong>4. Propiedad Intelectual</strong><br>
            Todo el contenido de este Sitio, incluyendo textos, gráficos, logotipos, imágenes, descripciones de productos y software, está protegido por leyes de propiedad intelectual y pertenece a Enzimlex (enzimlex.mirus.help) o a sus proveedores de contenido.</p>
            <p><strong>5. Enlaces a Terceros</strong><br>
            Nuestro Sitio puede contener enlaces a sitios web de terceros. No tenemos control sobre el contenido o las prácticas de privacidad de estos sitios y no asumimos responsabilidad por ellos. Dichos enlaces se proporcionan únicamente para su conveniencia.</p>
            <p><strong>6. Limitación de Responsabilidad</strong><br>
            Enzimlex (enzimlex.mirus.help) no será responsable por daños directos, indirectos, incidentales, consecuentes o punitivos que surjan del uso o la imposibilidad de usar nuestro Sitio o los productos adquiridos, incluyendo cualquier reacción adversa o resultado inesperado si no se siguen las recomendaciones de uso y las advertencias aquí establecidas, o si no se consulta a un profesional de la salud.</p>
            <p>Usted acepta que el uso de los productos es bajo su propio riesgo y después de haber considerado la información y las advertencias proporcionadas.</p>
            
            <p><strong>7. Modificaciones</strong><br>
            Nos reservamos el derecho de modificar estos Términos y Condiciones en cualquier momento. Las modificaciones entrarán en vigor inmediatamente después de su publicación en el Sitio.</p>
            <p><strong>8. Ley Aplicable y Jurisdicción</strong><br>
            Estos Términos y Condiciones se rigen por las leyes de los Estados Unidos Mexicanos. Cualquier disputa que surja en relación con estos términos se someterá a la jurisdicción exclusiva de los tribunales competentes en [Ciudad, Estado], México.</p>
            <p><strong>9. Contacto</strong><br>
            Si tiene preguntas sobre estos Términos y Condiciones, contáctenos en:</p>
            <p>Correo electrónico: [Tu Correo Electrónico]<br>
            Teléfono: +52 493 932 82 66<br>
            Dirección: <EMAIL></p>
            <p><em>Última actualización: 05.05.2025</em></p>
        </div>
    </div>


    <script>
        
        document.addEventListener('DOMContentLoaded', () => {
         

            // Footer current year
            document.getElementById('currentYear').textContent = new Date().getFullYear();

            // Modal Logic
            const openPrivacyModalLink = document.getElementById('openPrivacyModal');
            const openTermsModalLink = document.getElementById('openTermsModal');
            const privacyModal = document.getElementById('privacyModal');
            const termsModal = document.getElementById('termsModal');
            const closeButtons = document.querySelectorAll('.close-button');

            function openModal(modalElement) {
                if (modalElement) modalElement.style.display = 'block';
            }

            function closeModal(modalElement) {
                if (modalElement) modalElement.style.display = 'none';
            }

            if (openPrivacyModalLink && privacyModal) {
                openPrivacyModalLink.addEventListener('click', (event) => {
                    event.preventDefault();
                    openModal(privacyModal);
                });
            }

            if (openTermsModalLink && termsModal) {
                openTermsModalLink.addEventListener('click', (event) => {
                    event.preventDefault();
                    openModal(termsModal);
                });
            }

            closeButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const modalIdToClose = button.getAttribute('data-modal-close');
                    if (modalIdToClose) {
                        closeModal(document.getElementById(modalIdToClose));
                    }
                });
            });

            // Close modal if clicked outside of modal-content
            window.addEventListener('click', (event) => {
                if (event.target === privacyModal) {
                    closeModal(privacyModal);
                }
                if (event.target === termsModal) {
                    closeModal(termsModal);
                }
            });
        });
    </script>
    
</body>
</html>