{"$ref": "#/definitions/RawConfig", "$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"Assets": {"additionalProperties": false, "properties": {"binding": {"description": "Name of `env` binding property in the User Worker.", "type": "string"}, "directory": {"description": "Absolute path to assets directory", "type": "string"}, "experimental_serve_directly": {"description": "Deprecated; Inverse of run_worker_first. Should use run_worker_first instead", "type": "boolean"}, "html_handling": {"description": "How to handle HTML requests.", "enum": ["auto-trailing-slash", "force-trailing-slash", "drop-trailing-slash", "none"], "type": "string"}, "not_found_handling": {"description": "How to handle requests that do not match an asset.", "enum": ["single-page-application", "404-page", "none"], "type": "string"}, "run_worker_first": {"description": "If true, route every request to the User Worker, whether or not it matches an asset. If false, then respond to requests that match an asset with that asset directly.", "type": "boolean"}}, "type": "object"}, "CloudchamberConfig": {"additionalProperties": false, "description": "Configuration in wrangler for Cloudchamber", "properties": {"image": {"type": "string"}, "ipv4": {"type": "boolean"}, "location": {"type": "string"}, "memory": {"type": "string"}, "vcpu": {"type": "number"}}, "type": "object"}, "ConfigModuleRuleType": {"description": "The possible types for a `Rule`.", "enum": ["ESModule", "CommonJS", "CompiledWasm", "Text", "Data", "PythonModule", "PythonRequirement"], "type": "string"}, "ContainerApp": {"additionalProperties": false, "description": "Configuration for a container application", "properties": {"configuration": {"additionalProperties": false, "properties": {"image": {"type": "string"}, "labels": {"items": {"additionalProperties": false, "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"], "type": "object"}, "type": "array"}, "secrets": {"items": {"additionalProperties": false, "properties": {"name": {"type": "string"}, "secret": {"type": "string"}, "type": {"const": "env", "type": "string"}}, "required": ["name", "type", "secret"], "type": "object"}, "type": "array"}}, "required": ["image"], "type": "object"}, "constraints": {"additionalProperties": false, "properties": {"cities": {"items": {"type": "string"}, "type": "array"}, "regions": {"items": {"type": "string"}, "type": "array"}, "tier": {"type": "number"}}, "type": "object"}, "instances": {"type": "number"}, "name": {"type": "string"}, "scheduling_policy": {"enum": ["regional", "moon"], "type": "string"}}, "required": ["name", "instances", "configuration"], "type": "object"}, "CustomDomainRoute": {"additionalProperties": false, "properties": {"custom_domain": {"type": "boolean"}, "pattern": {"type": "string"}}, "required": ["pattern", "custom_domain"], "type": "object"}, "DeprecatedUpload": {"additionalProperties": false, "description": "Deprecated upload configuration.", "properties": {"dir": {"deprecated": true, "description": "The directory you wish to upload your Worker from, relative to the Wrangler configuration file.\n\nDefaults to the directory containing the Wrangler configuration file.", "type": "string"}, "format": {"deprecated": "We infer the format automatically now.", "description": "The format of the Worker script.", "enum": ["modules", "service-worker"], "type": "string"}, "main": {"deprecated": "This will be replaced by a command line argument.", "description": "The path to the Worker script, relative to `upload.dir`.", "type": "string"}, "rules": {"$ref": "#/definitions/Environment", "deprecated": "This is now defined at the top level `rules` field."}}, "type": "object"}, "DispatchNamespaceOutbound": {"additionalProperties": false, "properties": {"environment": {"description": "(Optional) Name of the environment handling the outbound requests.", "type": "string"}, "parameters": {"description": "(Optional) List of parameter names, for sending context from your dispatch Worker to the outbound handler", "items": {"type": "string"}, "type": "array"}, "service": {"description": "Name of the service handling the outbound requests", "type": "string"}}, "required": ["service"], "type": "object"}, "DurableObjectBindings": {"items": {"additionalProperties": false, "properties": {"class_name": {"description": "The exported class name of the Durable Object", "type": "string"}, "environment": {"description": "The service environment of the script_name to bind to", "type": "string"}, "name": {"description": "The name of the binding used to refer to the Durable Object", "type": "string"}, "script_name": {"description": "The script where the Durable Object is defined (if it's external to this Worker)", "type": "string"}}, "required": ["name", "class_name"], "type": "object"}, "type": "array"}, "DurableObjectMigration": {"additionalProperties": false, "description": "Configuration in wrangler for Durable Object Migrations", "properties": {"deleted_classes": {"description": "The Durable Objects being removed.", "items": {"type": "string"}, "type": "array"}, "new_classes": {"description": "The new Durable Objects being defined.", "items": {"type": "string"}, "type": "array"}, "new_sqlite_classes": {"description": "The new SQLite Durable Objects being defined.", "items": {"type": "string"}, "type": "array"}, "renamed_classes": {"description": "The Durable Objects being renamed.", "items": {"additionalProperties": false, "properties": {"from": {"type": "string"}, "to": {"type": "string"}}, "required": ["from", "to"], "type": "object"}, "type": "array"}, "tag": {"description": "A unique identifier for this migration.", "type": "string"}}, "required": ["tag"], "type": "object"}, "Environment": {"additionalProperties": false, "description": "The `Environment` interface declares all the configuration fields that can be specified for an environment.\n\nThis could be the top-level default environment, or a specific named environment.", "properties": {"account_id": {"description": "This is the ID of the account associated with your zone. You might have more than one account, so make sure to use the ID of the account associated with the zone/route you provide, if you provide one. It can also be specified through the CLOUDFLARE_ACCOUNT_ID environment variable.", "type": "string"}, "ai": {"additionalProperties": false, "default": {}, "description": "Binding to the AI project.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}, "staging": {"type": "boolean"}}, "required": ["binding"], "type": "object"}, "analytics_engine_datasets": {"default": [], "description": "Specifies analytics engine datasets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the dataset in the Worker.", "type": "string"}, "dataset": {"description": "The name of this dataset to write to.", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "assets": {"$ref": "#/definitions/Assets", "description": "Specify the directory of static assets to deploy/serve\n\nMore details at https://developers.cloudflare.com/workers/frameworks/"}, "base_dir": {"description": "The directory in which module rules should be evaluated when including additional files into a Worker deployment. This defaults to the directory containing the `main` entry point of the Worker if not specified.", "type": "string"}, "browser": {"additionalProperties": false, "default": {}, "description": "A browser that will be usable from the Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "build": {"additionalProperties": false, "default": {"watch_dir": "./src"}, "description": "Configures a custom build step to be run by <PERSON><PERSON><PERSON> when building your Worker.\n\nRefer to the [custom builds documentation](https://developers.cloudflare.com/workers/cli-wrangler/configuration#build) for more details.", "properties": {"command": {"description": "The command used to build your Worker. On Linux and macOS, the command is executed in the `sh` shell and the `cmd` shell for Windows. The `&&` and `||` shell operators may be used.", "type": "string"}, "cwd": {"description": "The directory in which the command is executed.", "type": "string"}, "upload": {"$ref": "#/definitions/DeprecatedUpload", "deprecated": true, "description": "Deprecated field previously used to configure the build and upload of the script."}, "watch_dir": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}], "description": "The directory to watch for changes while using wrangler dev, defaults to the current working directory"}}, "type": "object"}, "cloudchamber": {"$ref": "#/definitions/CloudchamberConfig", "default": {}, "description": "Cloudchamber configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment."}, "compatibility_date": {"description": "A date in the form yyyy-mm-dd, which will be used to determine which version of the Workers runtime is used.\n\nMore details at https://developers.cloudflare.com/workers/platform/compatibility-dates", "type": "string"}, "compatibility_flags": {"default": [], "description": "A list of flags that enable features from upcoming features of the Workers runtime, usually used together with compatibility_date.\n\nMore details at https://developers.cloudflare.com/workers/platform/compatibility-flags", "items": {"type": "string"}, "type": "array"}, "containers": {"additionalProperties": false, "description": "Container related configuration", "properties": {"app": {"default": {}, "description": "Container app configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/ContainerApp"}, "type": "array"}}, "required": ["app"], "type": "object"}, "d1_databases": {"default": [], "description": "Specifies D1 databases that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the D1 database in the Worker.", "type": "string"}, "database_id": {"description": "The UUID of this D1 database (not required).", "type": "string"}, "database_internal_env": {"description": "Internal use only.", "type": "string"}, "database_name": {"description": "The name of this D1 database.", "type": "string"}, "migrations_dir": {"description": "The path to the directory of migrations for this D1 database (defaults to './migrations').", "type": "string"}, "migrations_table": {"description": "The name of the migrations table for this D1 database (defaults to 'd1_migrations').", "type": "string"}, "preview_database_id": {"description": "The UUID of this D1 database for Wrangler Dev (if specified).", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "define": {"additionalProperties": {"type": "string"}, "default": {}, "description": "A map of values to substitute when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "type": "object"}, "dispatch_namespaces": {"default": [], "description": "Specifies namespace bindings that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "namespace": {"description": "The namespace to bind to.", "type": "string"}, "outbound": {"$ref": "#/definitions/DispatchNamespaceOutbound", "description": "Details about the outbound Worker which will handle outbound requests from your namespace"}}, "required": ["binding", "namespace"], "type": "object"}, "type": "array"}, "durable_objects": {"additionalProperties": false, "default": {"bindings": []}, "description": "A list of durable objects that your Worker should be bound to.\n\nFor more information about Durable Objects, see the documentation at https://developers.cloudflare.com/workers/learning/using-durable-objects\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"bindings": {"$ref": "#/definitions/DurableObjectBindings"}}, "required": ["bindings"], "type": "object"}, "find_additional_modules": {"description": "If true then <PERSON><PERSON><PERSON> will traverse the file tree below `base_dir`; Any files that match `rules` will be included in the deployed Worker. Defaults to true if `no_bundle` is true, otherwise false.", "type": "boolean"}, "first_party_worker": {"description": "Designates this Worker as an internal-only \"first-party\" Worker.", "type": "boolean"}, "hyperdrive": {"default": [], "description": "Specifies Hyperdrive configs that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the project in the Worker.", "type": "string"}, "id": {"description": "The id of the database.", "type": "string"}, "localConnectionString": {"description": "The local database connection string for `wrangler dev`", "type": "string"}}, "required": ["binding", "id"], "type": "object"}, "type": "array"}, "images": {"additionalProperties": false, "default": {}, "description": "Binding to Cloudflare Images\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "jsx_factory": {"default": "React.createElement", "description": "The function to use to replace jsx syntax.", "type": "string"}, "jsx_fragment": {"default": "React.Fragment", "description": "The function to use to replace jsx fragment syntax.", "type": "string"}, "kv_namespaces": {"default": [], "description": "These specify any Workers KV Namespaces you want to access from inside your Worker.\n\nTo learn more about KV Namespaces, see the documentation at https://developers.cloudflare.com/workers/learning/how-kv-works\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the KV Namespace", "type": "string"}, "id": {"description": "The ID of the KV namespace", "type": "string"}, "preview_id": {"description": "The ID of the KV namespace used during `wrangler dev`", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "limits": {"$ref": "#/definitions/UserLimits", "description": "Specify limits for runtime behavior. Only supported for the \"standard\" Usage Model"}, "logfwdr": {"additionalProperties": false, "default": {"bindings": []}, "description": "List of bindings that you will send to logfwdr", "properties": {"bindings": {"items": {"additionalProperties": false, "properties": {"destination": {"description": "The destination for this logged message", "type": "string"}, "name": {"description": "The binding name used to refer to logfwdr", "type": "string"}}, "required": ["name", "destination"], "type": "object"}, "type": "array"}}, "required": ["bindings"], "type": "object"}, "logpush": {"description": "Send Trace Events from this Worker to Workers Logpush.\n\nThis will not configure a corresponding Logpush job automatically.\n\nFor more information about Workers Logpush, see: https://blog.cloudflare.com/logpush-for-workers/", "type": "boolean"}, "main": {"description": "The entrypoint/path to the JavaScript file that will be executed.", "type": "string"}, "migrations": {"default": [], "description": "A list of migrations that should be uploaded with your Worker.\n\nThese define changes in your Durable Object declarations.\n\nMore details at https://developers.cloudflare.com/workers/learning/using-durable-objects#configuring-durable-object-classes-with-migrations", "items": {"$ref": "#/definitions/DurableObjectMigration"}, "type": "array"}, "minify": {"description": "Minify the script before uploading.", "type": "boolean"}, "mtls_certificates": {"default": [], "description": "Specifies a list of mTLS certificates that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the certificate in the Worker", "type": "string"}, "certificate_id": {"description": "The uuid of the uploaded mTLS certificate", "type": "string"}}, "required": ["binding", "certificate_id"], "type": "object"}, "type": "array"}, "name": {"description": "The name of your Worker. Alphanumeric + dashes only.", "type": "string"}, "no_bundle": {"description": "Skip internal build steps and directly deploy script", "type": "boolean"}, "node_compat": {"description": "Add polyfills for node builtin modules and globals", "type": "boolean"}, "observability": {"$ref": "#/definitions/Observability", "description": "Specify the observability behavior of the Worker."}, "pipelines": {"default": [], "description": "Specifies list of Pipelines bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "pipeline": {"description": "Name of the Pipeline to bind", "type": "string"}}, "required": ["binding", "pipeline"], "type": "object"}, "type": "array"}, "placement": {"additionalProperties": false, "description": "Specify how the Worker should be located to minimize round-trip time.\n\nMore details: https://developers.cloudflare.com/workers/platform/smart-placement/", "properties": {"hint": {"type": "string"}, "mode": {"enum": ["off", "smart"], "type": "string"}}, "required": ["mode"], "type": "object"}, "preserve_file_names": {"description": "Determines whether <PERSON><PERSON><PERSON> will preserve bundled file names. Defaults to false. If left unset, files will be named using the pattern ${fileHash}-${basename}, for example, `34de60b44167af5c5a709e62a4e20c4f18c9e3b6-favicon.ico`.", "type": "boolean"}, "preview_urls": {"default": true, "description": "Whether we use <version>-<name>.<subdomain>.workers.dev to serve Preview URLs for your Worker.", "type": "boolean"}, "queues": {"additionalProperties": false, "default": {"consumers": [], "producers": []}, "description": "Specifies Queues that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"consumers": {"description": "Consumer configuration", "items": {"additionalProperties": false, "properties": {"dead_letter_queue": {"description": "The queue to send messages that failed to be consumed.", "type": "string"}, "max_batch_size": {"description": "The maximum number of messages per batch", "type": "number"}, "max_batch_timeout": {"description": "The maximum number of seconds to wait to fill a batch with messages.", "type": "number"}, "max_concurrency": {"description": "The maximum number of concurrent consumer Worker invocations. Leaving this unset will allow your consumer to scale to the maximum concurrency needed to keep up with the message backlog.", "type": ["number", "null"]}, "max_retries": {"description": "The maximum number of retries for each message.", "type": "number"}, "queue": {"description": "The name of the queue from which this consumer should consume.", "type": "string"}, "retry_delay": {"description": "The number of seconds to wait before retrying a message", "type": "number"}, "type": {"description": "The consumer type, e.g., worker, http-pull, r2-bucket, etc. De<PERSON><PERSON> is worker.", "type": "string"}, "visibility_timeout_ms": {"description": "The number of milliseconds to wait for pulled messages to become visible again", "type": "number"}}, "required": ["queue"], "type": "object"}, "type": "array"}, "producers": {"description": "Producer bindings", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the Queue in the Worker.", "type": "string"}, "delivery_delay": {"description": "The number of seconds to wait before delivering a message", "type": "number"}, "queue": {"description": "The name of this Queue.", "type": "string"}}, "required": ["binding", "queue"], "type": "object"}, "type": "array"}}, "type": "object"}, "r2_buckets": {"default": [], "description": "Specifies R2 buckets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the R2 bucket in the Worker.", "type": "string"}, "bucket_name": {"description": "The name of this R2 bucket at the edge.", "type": "string"}, "jurisdiction": {"description": "The jurisdiction that the bucket exists in. Default if not present.", "type": "string"}, "preview_bucket_name": {"description": "The preview name of this R2 bucket at the edge.", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "route": {"$ref": "#/definitions/Route", "description": "A route that your Worker should be published to. Literally the same as routes, but only one. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker"}, "routes": {"description": "A list of routes that your Worker should be published to. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker (see `triggers`)", "items": {"$ref": "#/definitions/Route"}, "type": "array"}, "rules": {"description": "An ordered list of rules that define which modules to import, and what type to import them as. You will need to specify rules to use Text, Data, and CompiledWasm modules, or when you wish to have a .js file be treated as an ESModule instead of CommonJS.", "items": {"$ref": "#/definitions/Rule"}, "type": "array"}, "send_email": {"default": [], "description": "These specify bindings to send email from inside your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"allowed_destination_addresses": {"description": "If this binding should be restricted to a set of verified addresses", "items": {"type": "string"}, "type": "array"}, "destination_address": {"description": "If this binding should be restricted to a specific verified address", "type": "string"}, "name": {"description": "The binding name used to refer to the this binding", "type": "string"}}, "required": ["name"], "type": "object"}, "type": "array"}, "services": {"default": [], "description": "Specifies service bindings (Worker-to-Worker) that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "entrypoint": {"description": "Optionally, the entrypoint (named export) of the service to bind to.", "type": "string"}, "environment": {"description": "The environment of the service (e.g. production, staging, etc).", "type": "string"}, "service": {"description": "The name of the service.", "type": "string"}}, "required": ["binding", "service"], "type": "object"}, "type": "array"}, "tail_consumers": {"default": [], "description": "Specifies a list of Tail Workers that are bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/TailConsumer"}, "type": "array"}, "triggers": {"additionalProperties": false, "default": "{crons: undefined}", "description": "\"Cron\" definitions to trigger a Worker's \"scheduled\" function.\n\nLets you call Workers periodically, much like a cron job.\n\nMore details here https://developers.cloudflare.com/workers/platform/cron-triggers\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#triggers", "properties": {"crons": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "tsconfig": {"description": "Path to a custom tsconfig", "type": "string"}, "unsafe": {"additionalProperties": false, "default": {}, "description": "\"Unsafe\" tables for features that aren't directly supported by wrangler.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"bindings": {"description": "A set of bindings that should be put into a Worker's upload metadata without changes. These can be used to implement bindings for features that haven't released and aren't supported directly by wrangler or miniflare.", "items": {"additionalProperties": {}, "properties": {"name": {"type": "string"}, "type": {"type": "string"}}, "required": ["name", "type"], "type": "object"}, "type": "array"}, "capnp": {"anyOf": [{"additionalProperties": false, "properties": {"base_path": {"type": "string"}, "source_schemas": {"items": {"type": "string"}, "type": "array"}}, "required": ["base_path", "source_schemas"], "type": "object"}, {"additionalProperties": false, "properties": {"compiled_schema": {"type": "string"}}, "required": ["compiled_schema"], "type": "object"}], "description": "Used for internal capnp uploads for the Workers runtime"}, "metadata": {"additionalProperties": {}, "description": "Arbitrary key/value pairs that will be included in the uploaded metadata.  Values specified here will always be applied to metadata last, so can add new or override existing fields.", "type": "object"}}, "type": "object"}, "upload_source_maps": {"description": "Include source maps when uploading this worker.", "type": "boolean"}, "usage_model": {"description": "Specifies the Usage Model for your Worker. There are two options - [bundled](https://developers.cloudflare.com/workers/platform/limits#bundled-usage-model) and [unbound](https://developers.cloudflare.com/workers/platform/limits#unbound-usage-model). For newly created Workers, if the Usage Model is omitted it will be set to the [default Usage Model set on the account](https://dash.cloudflare.com/?account=workers/default-usage-model). For existing Workers, if the Usage Model is omitted, it will be set to the Usage Model configured in the dashboard for that Worker.", "enum": ["bundled", "unbound"], "type": "string"}, "vars": {"additionalProperties": {"anyOf": [{"type": "string"}, {"$ref": "#/definitions/Json"}]}, "default": {}, "description": "A map of environment variables to set when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "type": "object"}, "vectorize": {"default": [], "description": "Specifies Vectorize indexes that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the Vectorize index in the Worker.", "type": "string"}, "index_name": {"description": "The name of the index.", "type": "string"}}, "required": ["binding", "index_name"], "type": "object"}, "type": "array"}, "version_metadata": {"additionalProperties": false, "description": "Binding to the Worker Version's metadata", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "workers_dev": {"default": true, "description": "Whether we use <name>.<subdomain>.workers.dev to test and deploy your Worker.", "type": "boolean"}, "workflows": {"default": [], "description": "A list of workflows that your Worker should be bound to.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/WorkflowBinding"}, "type": "array"}, "zone_id": {"description": "TODO: remove this as it has been deprecated.\n\nThis is just here for now because the `route` commands use it. So we need to include it in this type so it is available.", "type": "string"}}, "required": ["analytics_engine_datasets", "build", "cloudchamber", "compatibility_flags", "containers", "d1_databases", "define", "dispatch_namespaces", "durable_objects", "hyperdrive", "jsx_factory", "jsx_fragment", "kv_namespaces", "logfwdr", "migrations", "mtls_certificates", "pipelines", "queues", "r2_buckets", "rules", "send_email", "triggers", "unsafe", "vars", "vectorize", "workflows"], "type": "object"}, "Json": {"anyOf": [{"$ref": "#/definitions/Literal"}, {"additionalProperties": {"$ref": "#/definitions/Json"}, "type": "object"}, {"items": {"$ref": "#/definitions/Json"}, "type": "array"}]}, "Literal": {"$ref": "#/definitions/TypeOf%3CZodUnion%3C%5Bdef-class-1315922706-6501-8772-1315922706-0-54395%2Cdef-class-1315922706-9299-10989-1315922706-0-54395%2Cdef-class-1315922706-12937-13365-1315922706-0-54395%2Cdef-class-1315922706-15083-15273-1315922706-0-54395%5D%3E%3E"}, "Observability": {"additionalProperties": false, "properties": {"enabled": {"description": "If observability is enabled for this Worker", "type": "boolean"}, "head_sampling_rate": {"description": "The sampling rate", "type": "number"}, "logs": {"additionalProperties": false, "properties": {"enabled": {"type": "boolean"}, "head_sampling_rate": {"description": "The sampling rate", "type": "number"}, "invocation_logs": {"description": "Set to false to disable invocation logs", "type": "boolean"}}, "type": "object"}}, "type": "object"}, "RawConfig": {"additionalProperties": false, "properties": {"$schema": {"type": "string"}, "account_id": {"description": "This is the ID of the account associated with your zone. You might have more than one account, so make sure to use the ID of the account associated with the zone/route you provide, if you provide one. It can also be specified through the CLOUDFLARE_ACCOUNT_ID environment variable.", "type": "string"}, "ai": {"additionalProperties": false, "default": {}, "description": "Binding to the AI project.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}, "staging": {"type": "boolean"}}, "required": ["binding"], "type": "object"}, "alias": {"additionalProperties": {"type": "string"}, "description": "A map of module aliases. Lets you swap out a module for any others. Corresponds with <PERSON>bu<PERSON><PERSON>'s `alias` config", "type": "object"}, "analytics_engine_datasets": {"default": [], "description": "Specifies analytics engine datasets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the dataset in the Worker.", "type": "string"}, "dataset": {"description": "The name of this dataset to write to.", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "assets": {"$ref": "#/definitions/Assets", "description": "Specify the directory of static assets to deploy/serve\n\nMore details at https://developers.cloudflare.com/workers/frameworks/"}, "base_dir": {"description": "The directory in which module rules should be evaluated when including additional files into a Worker deployment. This defaults to the directory containing the `main` entry point of the Worker if not specified.", "type": "string"}, "browser": {"additionalProperties": false, "default": {}, "description": "A browser that will be usable from the Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "build": {"additionalProperties": false, "default": {"watch_dir": "./src"}, "description": "Configures a custom build step to be run by <PERSON><PERSON><PERSON> when building your Worker.\n\nRefer to the [custom builds documentation](https://developers.cloudflare.com/workers/cli-wrangler/configuration#build) for more details.", "properties": {"command": {"description": "The command used to build your Worker. On Linux and macOS, the command is executed in the `sh` shell and the `cmd` shell for Windows. The `&&` and `||` shell operators may be used.", "type": "string"}, "cwd": {"description": "The directory in which the command is executed.", "type": "string"}, "upload": {"$ref": "#/definitions/DeprecatedUpload", "deprecated": true, "description": "Deprecated field previously used to configure the build and upload of the script."}, "watch_dir": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}], "description": "The directory to watch for changes while using wrangler dev, defaults to the current working directory"}}, "type": "object"}, "cloudchamber": {"$ref": "#/definitions/CloudchamberConfig", "default": {}, "description": "Cloudchamber configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment."}, "compatibility_date": {"description": "A date in the form yyyy-mm-dd, which will be used to determine which version of the Workers runtime is used.\n\nMore details at https://developers.cloudflare.com/workers/platform/compatibility-dates", "type": "string"}, "compatibility_flags": {"default": [], "description": "A list of flags that enable features from upcoming features of the Workers runtime, usually used together with compatibility_date.\n\nMore details at https://developers.cloudflare.com/workers/platform/compatibility-flags", "items": {"type": "string"}, "type": "array"}, "containers": {"additionalProperties": false, "description": "Container related configuration", "properties": {"app": {"default": {}, "description": "Container app configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/ContainerApp"}, "type": "array"}}, "required": ["app"], "type": "object"}, "d1_databases": {"default": [], "description": "Specifies D1 databases that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the D1 database in the Worker.", "type": "string"}, "database_id": {"description": "The UUID of this D1 database (not required).", "type": "string"}, "database_internal_env": {"description": "Internal use only.", "type": "string"}, "database_name": {"description": "The name of this D1 database.", "type": "string"}, "migrations_dir": {"description": "The path to the directory of migrations for this D1 database (defaults to './migrations').", "type": "string"}, "migrations_table": {"description": "The name of the migrations table for this D1 database (defaults to 'd1_migrations').", "type": "string"}, "preview_database_id": {"description": "The UUID of this D1 database for Wrangler Dev (if specified).", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "data_blobs": {"additionalProperties": {"type": "string"}, "description": "A list of data files that your worker should be bound to. This is the \"legacy\" way of binding to a data file. ES module workers should do proper module imports.", "type": "object"}, "define": {"additionalProperties": {"type": "string"}, "default": {}, "description": "A map of values to substitute when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "type": "object"}, "dev": {"$ref": "#/definitions/RawDevConfig", "description": "Options to configure the development server that your worker will use."}, "dispatch_namespaces": {"default": [], "description": "Specifies namespace bindings that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "namespace": {"description": "The namespace to bind to.", "type": "string"}, "outbound": {"$ref": "#/definitions/DispatchNamespaceOutbound", "description": "Details about the outbound Worker which will handle outbound requests from your namespace"}}, "required": ["binding", "namespace"], "type": "object"}, "type": "array"}, "durable_objects": {"additionalProperties": false, "default": {"bindings": []}, "description": "A list of durable objects that your Worker should be bound to.\n\nFor more information about Durable Objects, see the documentation at https://developers.cloudflare.com/workers/learning/using-durable-objects\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"bindings": {"$ref": "#/definitions/DurableObjectBindings"}}, "required": ["bindings"], "type": "object"}, "env": {"additionalProperties": {"$ref": "#/definitions/RawEnvironment"}, "default": {}, "description": "The `env` section defines overrides for the configuration for different environments.\n\nAll environment fields can be specified at the top level of the config indicating the default environment settings.\n\n- Some fields are inherited and overridable in each environment.\n- But some are not inherited and must be explicitly specified in every environment, if they are specified at the top level.\n\nFor more information, see the documentation at https://developers.cloudflare.com/workers/cli-wrangler/configuration#environments", "type": "object"}, "experimental_services": {"default": [], "deprecated": "DO NOT USE. We'd added this to test the new service binding system, but the proper way to test experimental features is to use `unsafe.bindings` configuration.", "description": "A list of services that your Worker should be bound to.", "items": {"additionalProperties": false, "properties": {"environment": {"description": "The Service's environment", "type": "string"}, "name": {"description": "The binding name used to refer to the Service", "type": "string"}, "service": {"description": "The name of the Service being bound", "type": "string"}}, "required": ["name", "service", "environment"], "type": "object"}, "type": "array"}, "find_additional_modules": {"description": "If true then <PERSON><PERSON><PERSON> will traverse the file tree below `base_dir`; Any files that match `rules` will be included in the deployed Worker. Defaults to true if `no_bundle` is true, otherwise false.", "type": "boolean"}, "first_party_worker": {"description": "Designates this Worker as an internal-only \"first-party\" Worker.", "type": "boolean"}, "hyperdrive": {"default": [], "description": "Specifies Hyperdrive configs that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the project in the Worker.", "type": "string"}, "id": {"description": "The id of the database.", "type": "string"}, "localConnectionString": {"description": "The local database connection string for `wrangler dev`", "type": "string"}}, "required": ["binding", "id"], "type": "object"}, "type": "array"}, "images": {"additionalProperties": false, "default": {}, "description": "Binding to Cloudflare Images\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "jsx_factory": {"default": "React.createElement", "description": "The function to use to replace jsx syntax.", "type": "string"}, "jsx_fragment": {"default": "React.Fragment", "description": "The function to use to replace jsx fragment syntax.", "type": "string"}, "keep_vars": {"default": false, "description": "By default, the Wrangler configuration file is the source of truth for your environment configuration, like a terraform file.\n\nIf you change your vars in the dashboard, wrangler *will* override/delete them on its next deploy.\n\nIf you want to keep your dashboard vars when wrangler deploys, set this field to true.", "type": "boolean"}, "kv-namespaces": {"deprecated": "DO NOT USE. This was a legacy bug from Wrangler v1, that we do not want to support.", "description": "Legacy way of defining KVNamespaces that is no longer supported.", "type": "string"}, "kv_namespaces": {"default": [], "description": "These specify any Workers KV Namespaces you want to access from inside your Worker.\n\nTo learn more about KV Namespaces, see the documentation at https://developers.cloudflare.com/workers/learning/how-kv-works\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the KV Namespace", "type": "string"}, "id": {"description": "The ID of the KV namespace", "type": "string"}, "preview_id": {"description": "The ID of the KV namespace used during `wrangler dev`", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "legacy_assets": {"anyOf": [{"additionalProperties": false, "properties": {"browser_TTL": {"type": "number"}, "bucket": {"type": "string"}, "exclude": {"items": {"type": "string"}, "type": "array"}, "include": {"items": {"type": "string"}, "type": "array"}, "serve_single_page_app": {"type": "boolean"}}, "required": ["bucket", "include", "exclude", "serve_single_page_app"], "type": "object"}, {"type": "string"}], "description": "Old behaviour of serving a folder of static assets with your Worker, without any additional code. This can either be a string, or an object with additional config fields. Will be deprecated in the near future in favor of `assets`."}, "legacy_env": {"description": "A boolean to enable \"legacy\" style wrangler environments (from Wrangler v1). These have been superseded by Services, but there may be projects that won't (or can't) use them. If you're using a legacy environment, you can set this to `true` to enable it.", "type": "boolean"}, "limits": {"$ref": "#/definitions/UserLimits", "description": "Specify limits for runtime behavior. Only supported for the \"standard\" Usage Model"}, "logfwdr": {"additionalProperties": false, "default": {"bindings": []}, "description": "List of bindings that you will send to logfwdr", "properties": {"bindings": {"items": {"additionalProperties": false, "properties": {"destination": {"description": "The destination for this logged message", "type": "string"}, "name": {"description": "The binding name used to refer to logfwdr", "type": "string"}}, "required": ["name", "destination"], "type": "object"}, "type": "array"}}, "required": ["bindings"], "type": "object"}, "logpush": {"description": "Send Trace Events from this Worker to Workers Logpush.\n\nThis will not configure a corresponding Logpush job automatically.\n\nFor more information about Workers Logpush, see: https://blog.cloudflare.com/logpush-for-workers/", "type": "boolean"}, "main": {"description": "The entrypoint/path to the JavaScript file that will be executed.", "type": "string"}, "migrations": {"default": [], "description": "A list of migrations that should be uploaded with your Worker.\n\nThese define changes in your Durable Object declarations.\n\nMore details at https://developers.cloudflare.com/workers/learning/using-durable-objects#configuring-durable-object-classes-with-migrations", "items": {"$ref": "#/definitions/DurableObjectMigration"}, "type": "array"}, "miniflare": {"deprecated": true, "description": "Configuration only used by a standalone use of the miniflare binary."}, "minify": {"description": "Minify the script before uploading.", "type": "boolean"}, "mtls_certificates": {"default": [], "description": "Specifies a list of mTLS certificates that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the certificate in the Worker", "type": "string"}, "certificate_id": {"description": "The uuid of the uploaded mTLS certificate", "type": "string"}}, "required": ["binding", "certificate_id"], "type": "object"}, "type": "array"}, "name": {"description": "The name of your Worker. Alphanumeric + dashes only.", "type": "string"}, "no_bundle": {"description": "Skip internal build steps and directly deploy script", "type": "boolean"}, "node_compat": {"description": "Add polyfills for node builtin modules and globals", "type": "boolean"}, "observability": {"$ref": "#/definitions/Observability", "description": "Specify the observability behavior of the Worker."}, "pages_build_output_dir": {"description": "The directory of static assets to serve.\n\nThe presence of this field in a Wrangler configuration file indicates a Pages project, and will prompt the handling of the configuration file according to the Pages-specific validation rules.", "type": "string"}, "pipelines": {"default": [], "description": "Specifies list of Pipelines bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "pipeline": {"description": "Name of the Pipeline to bind", "type": "string"}}, "required": ["binding", "pipeline"], "type": "object"}, "type": "array"}, "placement": {"additionalProperties": false, "description": "Specify how the Worker should be located to minimize round-trip time.\n\nMore details: https://developers.cloudflare.com/workers/platform/smart-placement/", "properties": {"hint": {"type": "string"}, "mode": {"enum": ["off", "smart"], "type": "string"}}, "required": ["mode"], "type": "object"}, "preserve_file_names": {"description": "Determines whether <PERSON><PERSON><PERSON> will preserve bundled file names. Defaults to false. If left unset, files will be named using the pattern ${fileHash}-${basename}, for example, `34de60b44167af5c5a709e62a4e20c4f18c9e3b6-favicon.ico`.", "type": "boolean"}, "preview_urls": {"default": true, "description": "Whether we use <version>-<name>.<subdomain>.workers.dev to serve Preview URLs for your Worker.", "type": "boolean"}, "queues": {"additionalProperties": false, "default": {"consumers": [], "producers": []}, "description": "Specifies Queues that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"consumers": {"description": "Consumer configuration", "items": {"additionalProperties": false, "properties": {"dead_letter_queue": {"description": "The queue to send messages that failed to be consumed.", "type": "string"}, "max_batch_size": {"description": "The maximum number of messages per batch", "type": "number"}, "max_batch_timeout": {"description": "The maximum number of seconds to wait to fill a batch with messages.", "type": "number"}, "max_concurrency": {"description": "The maximum number of concurrent consumer Worker invocations. Leaving this unset will allow your consumer to scale to the maximum concurrency needed to keep up with the message backlog.", "type": ["number", "null"]}, "max_retries": {"description": "The maximum number of retries for each message.", "type": "number"}, "queue": {"description": "The name of the queue from which this consumer should consume.", "type": "string"}, "retry_delay": {"description": "The number of seconds to wait before retrying a message", "type": "number"}, "type": {"description": "The consumer type, e.g., worker, http-pull, r2-bucket, etc. De<PERSON><PERSON> is worker.", "type": "string"}, "visibility_timeout_ms": {"description": "The number of milliseconds to wait for pulled messages to become visible again", "type": "number"}}, "required": ["queue"], "type": "object"}, "type": "array"}, "producers": {"description": "Producer bindings", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the Queue in the Worker.", "type": "string"}, "delivery_delay": {"description": "The number of seconds to wait before delivering a message", "type": "number"}, "queue": {"description": "The name of this Queue.", "type": "string"}}, "required": ["binding", "queue"], "type": "object"}, "type": "array"}}, "type": "object"}, "r2_buckets": {"default": [], "description": "Specifies R2 buckets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the R2 bucket in the Worker.", "type": "string"}, "bucket_name": {"description": "The name of this R2 bucket at the edge.", "type": "string"}, "jurisdiction": {"description": "The jurisdiction that the bucket exists in. Default if not present.", "type": "string"}, "preview_bucket_name": {"description": "The preview name of this R2 bucket at the edge.", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "route": {"$ref": "#/definitions/Route", "description": "A route that your Worker should be published to. Literally the same as routes, but only one. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker"}, "routes": {"description": "A list of routes that your Worker should be published to. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker (see `triggers`)", "items": {"$ref": "#/definitions/Route"}, "type": "array"}, "rules": {"description": "An ordered list of rules that define which modules to import, and what type to import them as. You will need to specify rules to use Text, Data, and CompiledWasm modules, or when you wish to have a .js file be treated as an ESModule instead of CommonJS.", "items": {"$ref": "#/definitions/Rule"}, "type": "array"}, "send_email": {"default": [], "description": "These specify bindings to send email from inside your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"allowed_destination_addresses": {"description": "If this binding should be restricted to a set of verified addresses", "items": {"type": "string"}, "type": "array"}, "destination_address": {"description": "If this binding should be restricted to a specific verified address", "type": "string"}, "name": {"description": "The binding name used to refer to the this binding", "type": "string"}}, "required": ["name"], "type": "object"}, "type": "array"}, "send_metrics": {"description": "Whether Wrangler should send usage metrics to Cloudflare for this project.\n\nWhen defined this will override any user settings. Otherwise, Wrangler will use the user's preference.", "type": "boolean"}, "services": {"default": [], "description": "Specifies service bindings (Worker-to-Worker) that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "entrypoint": {"description": "Optionally, the entrypoint (named export) of the service to bind to.", "type": "string"}, "environment": {"description": "The environment of the service (e.g. production, staging, etc).", "type": "string"}, "service": {"description": "The name of the service.", "type": "string"}}, "required": ["binding", "service"], "type": "object"}, "type": "array"}, "site": {"additionalProperties": false, "description": "The definition of a Worker Site, a feature that lets you upload static assets with your Worker.\n\nMore details at https://developers.cloudflare.com/workers/platform/sites", "properties": {"bucket": {"description": "The directory containing your static assets.\n\nIt must be a path relative to your Wrangler configuration file. Example: bucket = \"./public\"\n\nIf there is a `site` field then it must contain this `bucket` field.", "type": "string"}, "entry-point": {"deprecated": "DO NOT use this (it's a holdover from Wrangler v1.x). Either use the top level `main` field, or pass the path to your entry file as a command line argument.", "description": "The location of your Worker script.", "type": "string"}, "exclude": {"default": [], "description": "A list of .gitignore-style patterns that match files or directories in your bucket that should be excluded from uploads. Example: exclude = [\"ignore_dir\"]", "items": {"type": "string"}, "type": "array"}, "include": {"default": [], "description": "An exclusive list of .gitignore-style patterns that match file or directory names from your bucket location. Only matched items will be uploaded. Example: include = [\"upload_dir\"]", "items": {"type": "string"}, "type": "array"}}, "required": ["bucket"], "type": "object"}, "tail_consumers": {"default": [], "description": "Specifies a list of Tail Workers that are bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/TailConsumer"}, "type": "array"}, "text_blobs": {"additionalProperties": {"type": "string"}, "description": "A list of text files that your worker should be bound to. This is the \"legacy\" way of binding to a text file. ES module workers should do proper module imports.", "type": "object"}, "triggers": {"additionalProperties": false, "default": "{crons: undefined}", "description": "\"Cron\" definitions to trigger a Worker's \"scheduled\" function.\n\nLets you call Workers periodically, much like a cron job.\n\nMore details here https://developers.cloudflare.com/workers/platform/cron-triggers\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#triggers", "properties": {"crons": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "tsconfig": {"description": "Path to a custom tsconfig", "type": "string"}, "type": {"deprecated": "DO NOT USE THIS. Most common features now work out of the box with wrangler, including modules, jsx, typescript, etc. If you need anything more, use a custom build.", "description": "The project \"type\". A holdover from Wrangler v1.x. Valid values were \"webpack\", \"javascript\", and \"rust\".", "enum": ["webpack", "javascript", "rust"], "type": "string"}, "unsafe": {"additionalProperties": false, "default": {}, "description": "\"Unsafe\" tables for features that aren't directly supported by wrangler.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"bindings": {"description": "A set of bindings that should be put into a Worker's upload metadata without changes. These can be used to implement bindings for features that haven't released and aren't supported directly by wrangler or miniflare.", "items": {"additionalProperties": {}, "properties": {"name": {"type": "string"}, "type": {"type": "string"}}, "required": ["name", "type"], "type": "object"}, "type": "array"}, "capnp": {"anyOf": [{"additionalProperties": false, "properties": {"base_path": {"type": "string"}, "source_schemas": {"items": {"type": "string"}, "type": "array"}}, "required": ["base_path", "source_schemas"], "type": "object"}, {"additionalProperties": false, "properties": {"compiled_schema": {"type": "string"}}, "required": ["compiled_schema"], "type": "object"}], "description": "Used for internal capnp uploads for the Workers runtime"}, "metadata": {"additionalProperties": {}, "description": "Arbitrary key/value pairs that will be included in the uploaded metadata.  Values specified here will always be applied to metadata last, so can add new or override existing fields.", "type": "object"}}, "type": "object"}, "upload_source_maps": {"description": "Include source maps when uploading this worker.", "type": "boolean"}, "usage_model": {"description": "Specifies the Usage Model for your Worker. There are two options - [bundled](https://developers.cloudflare.com/workers/platform/limits#bundled-usage-model) and [unbound](https://developers.cloudflare.com/workers/platform/limits#unbound-usage-model). For newly created Workers, if the Usage Model is omitted it will be set to the [default Usage Model set on the account](https://dash.cloudflare.com/?account=workers/default-usage-model). For existing Workers, if the Usage Model is omitted, it will be set to the Usage Model configured in the dashboard for that Worker.", "enum": ["bundled", "unbound"], "type": "string"}, "vars": {"additionalProperties": {"anyOf": [{"type": "string"}, {"$ref": "#/definitions/Json"}]}, "default": {}, "description": "A map of environment variables to set when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "type": "object"}, "vectorize": {"default": [], "description": "Specifies Vectorize indexes that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the Vectorize index in the Worker.", "type": "string"}, "index_name": {"description": "The name of the index.", "type": "string"}}, "required": ["binding", "index_name"], "type": "object"}, "type": "array"}, "version_metadata": {"additionalProperties": false, "description": "Binding to the Worker Version's metadata", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "wasm_modules": {"additionalProperties": {"type": "string"}, "description": "A list of wasm modules that your worker should be bound to. This is the \"legacy\" way of binding to a wasm module. ES module workers should do proper module imports.", "type": "object"}, "webpack_config": {"deprecated": "DO NOT USE THIS. Most common features now work out of the box with wrangler, including modules, jsx, typescript, etc. If you need anything more, use a custom build.", "description": "Path to the webpack config to use when building your worker. A holdover from Wrangler v1.x, used with `type: \"webpack\"`.", "type": "string"}, "workers_dev": {"default": true, "description": "Whether we use <name>.<subdomain>.workers.dev to test and deploy your Worker.", "type": "boolean"}, "workflows": {"default": [], "description": "A list of workflows that your Worker should be bound to.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/WorkflowBinding"}, "type": "array"}, "zone_id": {"description": "TODO: remove this as it has been deprecated.\n\nThis is just here for now because the `route` commands use it. So we need to include it in this type so it is available.", "type": "string"}}, "type": "object"}, "RawDevConfig": {"additionalProperties": false, "properties": {"host": {"description": "Host to forward requests to, defaults to the host of the first route of project", "type": "string"}, "inspector_port": {"default": 9229, "description": "Port for the local dev server's inspector to listen on", "type": "number"}, "ip": {"default": "localhost", "description": "IP address for the local dev server to listen on,", "type": "string"}, "local_protocol": {"default": "http", "description": "Protocol that local wrangler dev server listens to requests on.", "enum": ["http", "https"], "type": "string"}, "port": {"default": 8787, "description": "Port for the local dev server to listen on", "type": "number"}, "upstream_protocol": {"default": "https", "description": "Protocol that wrangler dev forwards requests on\n\nSetting this to `http` is not currently implemented for remote mode. See https://github.com/cloudflare/workers-sdk/issues/583", "enum": ["https", "http"], "type": "string"}}, "type": "object"}, "RawEnvironment": {"additionalProperties": false, "description": "The raw environment configuration that we read from the config file.\n\nAll the properties are optional, and will be replaced with defaults in the configuration that is used in the rest of the codebase.", "properties": {"account_id": {"description": "This is the ID of the account associated with your zone. You might have more than one account, so make sure to use the ID of the account associated with the zone/route you provide, if you provide one. It can also be specified through the CLOUDFLARE_ACCOUNT_ID environment variable.", "type": "string"}, "ai": {"additionalProperties": false, "default": {}, "description": "Binding to the AI project.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}, "staging": {"type": "boolean"}}, "required": ["binding"], "type": "object"}, "analytics_engine_datasets": {"default": [], "description": "Specifies analytics engine datasets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the dataset in the Worker.", "type": "string"}, "dataset": {"description": "The name of this dataset to write to.", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "assets": {"$ref": "#/definitions/Assets", "description": "Specify the directory of static assets to deploy/serve\n\nMore details at https://developers.cloudflare.com/workers/frameworks/"}, "base_dir": {"description": "The directory in which module rules should be evaluated when including additional files into a Worker deployment. This defaults to the directory containing the `main` entry point of the Worker if not specified.", "type": "string"}, "browser": {"additionalProperties": false, "default": {}, "description": "A browser that will be usable from the Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "build": {"additionalProperties": false, "default": {"watch_dir": "./src"}, "description": "Configures a custom build step to be run by <PERSON><PERSON><PERSON> when building your Worker.\n\nRefer to the [custom builds documentation](https://developers.cloudflare.com/workers/cli-wrangler/configuration#build) for more details.", "properties": {"command": {"description": "The command used to build your Worker. On Linux and macOS, the command is executed in the `sh` shell and the `cmd` shell for Windows. The `&&` and `||` shell operators may be used.", "type": "string"}, "cwd": {"description": "The directory in which the command is executed.", "type": "string"}, "upload": {"$ref": "#/definitions/DeprecatedUpload", "deprecated": true, "description": "Deprecated field previously used to configure the build and upload of the script."}, "watch_dir": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}], "description": "The directory to watch for changes while using wrangler dev, defaults to the current working directory"}}, "type": "object"}, "cloudchamber": {"$ref": "#/definitions/CloudchamberConfig", "default": {}, "description": "Cloudchamber configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment."}, "compatibility_date": {"description": "A date in the form yyyy-mm-dd, which will be used to determine which version of the Workers runtime is used.\n\nMore details at https://developers.cloudflare.com/workers/platform/compatibility-dates", "type": "string"}, "compatibility_flags": {"default": [], "description": "A list of flags that enable features from upcoming features of the Workers runtime, usually used together with compatibility_date.\n\nMore details at https://developers.cloudflare.com/workers/platform/compatibility-flags", "items": {"type": "string"}, "type": "array"}, "containers": {"additionalProperties": false, "description": "Container related configuration", "properties": {"app": {"default": {}, "description": "Container app configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/ContainerApp"}, "type": "array"}}, "required": ["app"], "type": "object"}, "d1_databases": {"default": [], "description": "Specifies D1 databases that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the D1 database in the Worker.", "type": "string"}, "database_id": {"description": "The UUID of this D1 database (not required).", "type": "string"}, "database_internal_env": {"description": "Internal use only.", "type": "string"}, "database_name": {"description": "The name of this D1 database.", "type": "string"}, "migrations_dir": {"description": "The path to the directory of migrations for this D1 database (defaults to './migrations').", "type": "string"}, "migrations_table": {"description": "The name of the migrations table for this D1 database (defaults to 'd1_migrations').", "type": "string"}, "preview_database_id": {"description": "The UUID of this D1 database for Wrangler Dev (if specified).", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "define": {"additionalProperties": {"type": "string"}, "default": {}, "description": "A map of values to substitute when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "type": "object"}, "dispatch_namespaces": {"default": [], "description": "Specifies namespace bindings that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "namespace": {"description": "The namespace to bind to.", "type": "string"}, "outbound": {"$ref": "#/definitions/DispatchNamespaceOutbound", "description": "Details about the outbound Worker which will handle outbound requests from your namespace"}}, "required": ["binding", "namespace"], "type": "object"}, "type": "array"}, "durable_objects": {"additionalProperties": false, "default": {"bindings": []}, "description": "A list of durable objects that your Worker should be bound to.\n\nFor more information about Durable Objects, see the documentation at https://developers.cloudflare.com/workers/learning/using-durable-objects\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"bindings": {"$ref": "#/definitions/DurableObjectBindings"}}, "required": ["bindings"], "type": "object"}, "experimental_services": {"default": [], "deprecated": "DO NOT USE. We'd added this to test the new service binding system, but the proper way to test experimental features is to use `unsafe.bindings` configuration.", "description": "A list of services that your Worker should be bound to.", "items": {"additionalProperties": false, "properties": {"environment": {"description": "The Service's environment", "type": "string"}, "name": {"description": "The binding name used to refer to the Service", "type": "string"}, "service": {"description": "The name of the Service being bound", "type": "string"}}, "required": ["name", "service", "environment"], "type": "object"}, "type": "array"}, "find_additional_modules": {"description": "If true then <PERSON><PERSON><PERSON> will traverse the file tree below `base_dir`; Any files that match `rules` will be included in the deployed Worker. Defaults to true if `no_bundle` is true, otherwise false.", "type": "boolean"}, "first_party_worker": {"description": "Designates this Worker as an internal-only \"first-party\" Worker.", "type": "boolean"}, "hyperdrive": {"default": [], "description": "Specifies Hyperdrive configs that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the project in the Worker.", "type": "string"}, "id": {"description": "The id of the database.", "type": "string"}, "localConnectionString": {"description": "The local database connection string for `wrangler dev`", "type": "string"}}, "required": ["binding", "id"], "type": "object"}, "type": "array"}, "images": {"additionalProperties": false, "default": {}, "description": "Binding to Cloudflare Images\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "jsx_factory": {"default": "React.createElement", "description": "The function to use to replace jsx syntax.", "type": "string"}, "jsx_fragment": {"default": "React.Fragment", "description": "The function to use to replace jsx fragment syntax.", "type": "string"}, "kv-namespaces": {"deprecated": "DO NOT USE. This was a legacy bug from Wrangler v1, that we do not want to support.", "description": "Legacy way of defining KVNamespaces that is no longer supported.", "type": "string"}, "kv_namespaces": {"default": [], "description": "These specify any Workers KV Namespaces you want to access from inside your Worker.\n\nTo learn more about KV Namespaces, see the documentation at https://developers.cloudflare.com/workers/learning/how-kv-works\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the KV Namespace", "type": "string"}, "id": {"description": "The ID of the KV namespace", "type": "string"}, "preview_id": {"description": "The ID of the KV namespace used during `wrangler dev`", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "limits": {"$ref": "#/definitions/UserLimits", "description": "Specify limits for runtime behavior. Only supported for the \"standard\" Usage Model"}, "logfwdr": {"additionalProperties": false, "default": {"bindings": []}, "description": "List of bindings that you will send to logfwdr", "properties": {"bindings": {"items": {"additionalProperties": false, "properties": {"destination": {"description": "The destination for this logged message", "type": "string"}, "name": {"description": "The binding name used to refer to logfwdr", "type": "string"}}, "required": ["name", "destination"], "type": "object"}, "type": "array"}}, "required": ["bindings"], "type": "object"}, "logpush": {"description": "Send Trace Events from this Worker to Workers Logpush.\n\nThis will not configure a corresponding Logpush job automatically.\n\nFor more information about Workers Logpush, see: https://blog.cloudflare.com/logpush-for-workers/", "type": "boolean"}, "main": {"description": "The entrypoint/path to the JavaScript file that will be executed.", "type": "string"}, "migrations": {"default": [], "description": "A list of migrations that should be uploaded with your Worker.\n\nThese define changes in your Durable Object declarations.\n\nMore details at https://developers.cloudflare.com/workers/learning/using-durable-objects#configuring-durable-object-classes-with-migrations", "items": {"$ref": "#/definitions/DurableObjectMigration"}, "type": "array"}, "minify": {"description": "Minify the script before uploading.", "type": "boolean"}, "mtls_certificates": {"default": [], "description": "Specifies a list of mTLS certificates that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the certificate in the Worker", "type": "string"}, "certificate_id": {"description": "The uuid of the uploaded mTLS certificate", "type": "string"}}, "required": ["binding", "certificate_id"], "type": "object"}, "type": "array"}, "name": {"description": "The name of your Worker. Alphanumeric + dashes only.", "type": "string"}, "no_bundle": {"description": "Skip internal build steps and directly deploy script", "type": "boolean"}, "node_compat": {"description": "Add polyfills for node builtin modules and globals", "type": "boolean"}, "observability": {"$ref": "#/definitions/Observability", "description": "Specify the observability behavior of the Worker."}, "pipelines": {"default": [], "description": "Specifies list of Pipelines bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "pipeline": {"description": "Name of the Pipeline to bind", "type": "string"}}, "required": ["binding", "pipeline"], "type": "object"}, "type": "array"}, "placement": {"additionalProperties": false, "description": "Specify how the Worker should be located to minimize round-trip time.\n\nMore details: https://developers.cloudflare.com/workers/platform/smart-placement/", "properties": {"hint": {"type": "string"}, "mode": {"enum": ["off", "smart"], "type": "string"}}, "required": ["mode"], "type": "object"}, "preserve_file_names": {"description": "Determines whether <PERSON><PERSON><PERSON> will preserve bundled file names. Defaults to false. If left unset, files will be named using the pattern ${fileHash}-${basename}, for example, `34de60b44167af5c5a709e62a4e20c4f18c9e3b6-favicon.ico`.", "type": "boolean"}, "preview_urls": {"default": true, "description": "Whether we use <version>-<name>.<subdomain>.workers.dev to serve Preview URLs for your Worker.", "type": "boolean"}, "queues": {"additionalProperties": false, "default": {"consumers": [], "producers": []}, "description": "Specifies Queues that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"consumers": {"description": "Consumer configuration", "items": {"additionalProperties": false, "properties": {"dead_letter_queue": {"description": "The queue to send messages that failed to be consumed.", "type": "string"}, "max_batch_size": {"description": "The maximum number of messages per batch", "type": "number"}, "max_batch_timeout": {"description": "The maximum number of seconds to wait to fill a batch with messages.", "type": "number"}, "max_concurrency": {"description": "The maximum number of concurrent consumer Worker invocations. Leaving this unset will allow your consumer to scale to the maximum concurrency needed to keep up with the message backlog.", "type": ["number", "null"]}, "max_retries": {"description": "The maximum number of retries for each message.", "type": "number"}, "queue": {"description": "The name of the queue from which this consumer should consume.", "type": "string"}, "retry_delay": {"description": "The number of seconds to wait before retrying a message", "type": "number"}, "type": {"description": "The consumer type, e.g., worker, http-pull, r2-bucket, etc. De<PERSON><PERSON> is worker.", "type": "string"}, "visibility_timeout_ms": {"description": "The number of milliseconds to wait for pulled messages to become visible again", "type": "number"}}, "required": ["queue"], "type": "object"}, "type": "array"}, "producers": {"description": "Producer bindings", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the Queue in the Worker.", "type": "string"}, "delivery_delay": {"description": "The number of seconds to wait before delivering a message", "type": "number"}, "queue": {"description": "The name of this Queue.", "type": "string"}}, "required": ["binding", "queue"], "type": "object"}, "type": "array"}}, "type": "object"}, "r2_buckets": {"default": [], "description": "Specifies R2 buckets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the R2 bucket in the Worker.", "type": "string"}, "bucket_name": {"description": "The name of this R2 bucket at the edge.", "type": "string"}, "jurisdiction": {"description": "The jurisdiction that the bucket exists in. Default if not present.", "type": "string"}, "preview_bucket_name": {"description": "The preview name of this R2 bucket at the edge.", "type": "string"}}, "required": ["binding"], "type": "object"}, "type": "array"}, "route": {"$ref": "#/definitions/Route", "description": "A route that your Worker should be published to. Literally the same as routes, but only one. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker"}, "routes": {"description": "A list of routes that your Worker should be published to. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker (see `triggers`)", "items": {"$ref": "#/definitions/Route"}, "type": "array"}, "rules": {"description": "An ordered list of rules that define which modules to import, and what type to import them as. You will need to specify rules to use Text, Data, and CompiledWasm modules, or when you wish to have a .js file be treated as an ESModule instead of CommonJS.", "items": {"$ref": "#/definitions/Rule"}, "type": "array"}, "send_email": {"default": [], "description": "These specify bindings to send email from inside your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"allowed_destination_addresses": {"description": "If this binding should be restricted to a set of verified addresses", "items": {"type": "string"}, "type": "array"}, "destination_address": {"description": "If this binding should be restricted to a specific verified address", "type": "string"}, "name": {"description": "The binding name used to refer to the this binding", "type": "string"}}, "required": ["name"], "type": "object"}, "type": "array"}, "services": {"default": [], "description": "Specifies service bindings (Worker-to-Worker) that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the bound service.", "type": "string"}, "entrypoint": {"description": "Optionally, the entrypoint (named export) of the service to bind to.", "type": "string"}, "environment": {"description": "The environment of the service (e.g. production, staging, etc).", "type": "string"}, "service": {"description": "The name of the service.", "type": "string"}}, "required": ["binding", "service"], "type": "object"}, "type": "array"}, "tail_consumers": {"default": [], "description": "Specifies a list of Tail Workers that are bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/TailConsumer"}, "type": "array"}, "triggers": {"additionalProperties": false, "default": "{crons: undefined}", "description": "\"Cron\" definitions to trigger a Worker's \"scheduled\" function.\n\nLets you call Workers periodically, much like a cron job.\n\nMore details here https://developers.cloudflare.com/workers/platform/cron-triggers\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#triggers", "properties": {"crons": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "tsconfig": {"description": "Path to a custom tsconfig", "type": "string"}, "unsafe": {"additionalProperties": false, "default": {}, "description": "\"Unsafe\" tables for features that aren't directly supported by wrangler.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "properties": {"bindings": {"description": "A set of bindings that should be put into a Worker's upload metadata without changes. These can be used to implement bindings for features that haven't released and aren't supported directly by wrangler or miniflare.", "items": {"additionalProperties": {}, "properties": {"name": {"type": "string"}, "type": {"type": "string"}}, "required": ["name", "type"], "type": "object"}, "type": "array"}, "capnp": {"anyOf": [{"additionalProperties": false, "properties": {"base_path": {"type": "string"}, "source_schemas": {"items": {"type": "string"}, "type": "array"}}, "required": ["base_path", "source_schemas"], "type": "object"}, {"additionalProperties": false, "properties": {"compiled_schema": {"type": "string"}}, "required": ["compiled_schema"], "type": "object"}], "description": "Used for internal capnp uploads for the Workers runtime"}, "metadata": {"additionalProperties": {}, "description": "Arbitrary key/value pairs that will be included in the uploaded metadata.  Values specified here will always be applied to metadata last, so can add new or override existing fields.", "type": "object"}}, "type": "object"}, "upload_source_maps": {"description": "Include source maps when uploading this worker.", "type": "boolean"}, "usage_model": {"description": "Specifies the Usage Model for your Worker. There are two options - [bundled](https://developers.cloudflare.com/workers/platform/limits#bundled-usage-model) and [unbound](https://developers.cloudflare.com/workers/platform/limits#unbound-usage-model). For newly created Workers, if the Usage Model is omitted it will be set to the [default Usage Model set on the account](https://dash.cloudflare.com/?account=workers/default-usage-model). For existing Workers, if the Usage Model is omitted, it will be set to the Usage Model configured in the dashboard for that Worker.", "enum": ["bundled", "unbound"], "type": "string"}, "vars": {"additionalProperties": {"anyOf": [{"type": "string"}, {"$ref": "#/definitions/Json"}]}, "default": {}, "description": "A map of environment variables to set when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "type": "object"}, "vectorize": {"default": [], "description": "Specifies Vectorize indexes that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"additionalProperties": false, "properties": {"binding": {"description": "The binding name used to refer to the Vectorize index in the Worker.", "type": "string"}, "index_name": {"description": "The name of the index.", "type": "string"}}, "required": ["binding", "index_name"], "type": "object"}, "type": "array"}, "version_metadata": {"additionalProperties": false, "description": "Binding to the Worker Version's metadata", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "type": "object"}, "workers_dev": {"default": true, "description": "Whether we use <name>.<subdomain>.workers.dev to test and deploy your Worker.", "type": "boolean"}, "workflows": {"default": [], "description": "A list of workflows that your Worker should be bound to.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "items": {"$ref": "#/definitions/WorkflowBinding"}, "type": "array"}, "zone_id": {"description": "TODO: remove this as it has been deprecated.\n\nThis is just here for now because the `route` commands use it. So we need to include it in this type so it is available.", "type": "string"}}, "type": "object"}, "Route": {"anyOf": [{"type": "string"}, {"$ref": "#/definitions/ZoneIdRoute"}, {"$ref": "#/definitions/ZoneNameRoute"}, {"$ref": "#/definitions/CustomDomainRoute"}]}, "Rule": {"additionalProperties": false, "description": "A bundling resolver rule, defining the modules type for paths that match the specified globs.", "properties": {"fallthrough": {"type": "boolean"}, "globs": {"items": {"type": "string"}, "type": "array"}, "type": {"$ref": "#/definitions/ConfigModuleRuleType"}}, "required": ["type", "globs"], "type": "object"}, "TailConsumer": {"additionalProperties": false, "properties": {"environment": {"description": "(Optional) The environment of the service.", "type": "string"}, "service": {"description": "The name of the service tail events will be forwarded to.", "type": "string"}}, "required": ["service"], "type": "object"}, "TypeOf<ZodUnion<[def-class-1315922706-6501-8772-1315922706-0-54395,def-class-1315922706-9299-10989-1315922706-0-54395,def-class-1315922706-12937-13365-1315922706-0-54395,def-class-1315922706-15083-15273-1315922706-0-54395]>>": {"type": ["string", "number", "boolean", "null"]}, "UserLimits": {"additionalProperties": false, "properties": {"cpu_ms": {"description": "Maximum allowed CPU time for a Worker's invocation in milliseconds", "type": "number"}}, "required": ["cpu_ms"], "type": "object"}, "WorkflowBinding": {"additionalProperties": false, "properties": {"binding": {"description": "The name of the binding used to refer to the Workflow", "type": "string"}, "class_name": {"description": "The exported class name of the Workflow", "type": "string"}, "name": {"description": "The name of the Workflow", "type": "string"}, "script_name": {"description": "The script where the Workflow is defined (if it's external to this Worker)", "type": "string"}}, "required": ["binding", "name", "class_name"], "type": "object"}, "ZoneIdRoute": {"additionalProperties": false, "properties": {"custom_domain": {"type": "boolean"}, "pattern": {"type": "string"}, "zone_id": {"type": "string"}}, "required": ["pattern", "zone_id"], "type": "object"}, "ZoneNameRoute": {"additionalProperties": false, "properties": {"custom_domain": {"type": "boolean"}, "pattern": {"type": "string"}, "zone_name": {"type": "string"}}, "required": ["pattern", "zone_name"], "type": "object"}}}