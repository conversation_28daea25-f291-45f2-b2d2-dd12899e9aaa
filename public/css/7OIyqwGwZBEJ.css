.ac_footer {
    position: relative;
    top: 10px;
    height: 0;
    text-align: center;
    margin-bottom: 70px;
    color: #A12000;
}

.ac_footer a {
    color: #A12000;
}

.ac_footer p {
    text-align: center;
}


img[width="1"] {
    display: none !important;
}

*,
*:before,
*:after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}


article,
aside,
section {
    display: block;
}

html {
    width: 100%;
    /*scroll-behavior: smooth;*/
}

body {
    font-size: 18px;
    line-height: 1.33;
    font-family: "Roboto", sans-serif;
    padding: 0;
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    color: #000;
    background: #f5f6f8;
    font-weight: 400;
    width: 100%;
}

.d-mobile {
    display: none !important;
}

.clearfix {
    clear: both;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

a {
    color: #1b52bb;
    text-decoration: underline;
}

a:hover {
    text-decoration: none;
}

p {
    margin: 0 0 18px;
}

.img-responsive {
    max-width: 100%;
}

.d-center {
    display: block;
    margin: 0 auto 15px;
}

.wrapper {
    overflow: hidden;
}

.text-center {
    text-align: center;
}

.text-uppercase {
    text-transform: uppercase;
}

.header-top {

    padding: 6px 0 4px;
    background: #2e4b83;
    -webkit-box-shadow: 0 0 17px rgba(166, 181, 210, 0.29);
    box-shadow: 0 0 17px rgba(166, 181, 210, 0.29);
    overflow: hidden;
    position: relative;
}

.header-top:after {
    content: "";
    width: 100%;
    height: 1px;
    border-bottom: 1px dashed rgba(255, 255, 255, .24);
    position: absolute;
    left: 0;
    bottom: 1px;
}

.top-menu {

    float: left;
}

.top-menu a {
    font-size: 13px;
    text-transform: uppercase;
    color: #fff;
    display: inline-block;
    text-decoration: none;
    padding: 2px 16px;
    border-right: 1px dashed rgba(255, 255, 255, .24);
}

.top-menu a:first-child {
    padding-left: 0;
}

.top-menu a:hover {
    text-decoration: underline;
}

.b-social {
    float: right;
}

.b-social a {

    display: inline-block;
    width: 23px;
    height: 23px;
    background: url("../images/7gWBFtbqjTxl.png") no-repeat;
    background-size: auto 23px;
    margin-left: 5px;
}

.b-social a.fb {
    background-position: 0 0;
}

.b-social a.tw {
    background-position: -73px 0;
}

.b-social a.in {
    background-position: -24px 0;
}

.b-social a.pn {
    background-position: -49px 0;
}

.header-middle {
    overflow: hidden;
    padding: 16px;
}

.b-logo {
    margin-right: -60px;
    float: left;
    text-decoration: none;
    margin-left: 5px;
}

.logo-red {
    color: #fff;
    background: #ed1b28;
    display: inline-block;
    position: relative;
    font-size: 15px;
    line-height: 1.15;
    padding: 0 5px;
    margin: 0 0 0 10px;
}

.logo-red:before,
.logo-red:after {
    content: "";
    border-top: 17px solid #ed1b28;
    border-left: 6px solid transparent;
    position: absolute;
    top: 0
}

.logo-red:before {
    left: -6px;
}

.logo-red:after {
    right: -6px;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.logo-blue {
    font-weight: 900;
    font-size: 28px;
    display: block;
    background: #2e4b83;
    position: relative;
    color: #fff;
    text-transform: uppercase;
    line-height: 1.15;
    padding: 0 12px;
    margin: -2px 0 -1px 10px;
    letter-spacing: 1px;
}

.logo-blue:before,
.logo-blue:after {
    content: "";
    border-bottom: 32px solid #2e4b83;
    border-left: 10px solid transparent;
    position: absolute;
    top: 0
}

.logo-blue:before {
    left: -10px;
}

.logo-blue:after {
    right: -10px;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.logo-black {
    color: #fff;
    display: inline-block;
    background: #000;
    font-size: 16px;
    position: relative;
    padding: 0 3px;
    line-height: 1.4;
}

.logo-black:before,
.logo-black:after {
    content: "";
    border-bottom: 22px solid #000;
    border-left: 5px solid transparent;
    position: absolute;
    top: 0;

}

.logo-black:before {
    left: -5px;
}

.logo-black:after {
    right: -5px;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.logo-white {
    background: #fff;
    position: relative;
    font-size: 13px;
    text-transform: uppercase;
    color: #000;
    display: inline-block;
    line-height: 1.7;
}

.logo-white:before,
.logo-white:after {
    content: "";
    border-bottom: 22px solid #fff;
    border-left: 5px solid transparent;
    position: absolute;
    top: 0
}

.logo-white:before {
    left: -5px;
}

.logo-white:after {
    right: -5px;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.top-bnr {
    float: right;
    width: 63.5%;
    color: #fff;
    text-align: right;
    background: url() no-repeat;
    padding: 18px 25px;
}

.top-bnr div {
    font-size: 32px;
    font-weight: 900;
    line-height: 1;
}

.header-bottom {
    background: #fff;
    border-bottom: 1px dashed rgba(0, 0, 0, .15);
}

.bottom-menu {
    font-size: 13px;
    display: table;
    width: 100%;
    text-align: center;
    padding: 0 5px;
}

.bottom-menu a {
    color: #000;
    text-decoration: none;
    font-size: 13px;
    font-weight: 700;
    padding: 11px 0;
    display: table-cell;
    width: 1%;
    text-transform: uppercase;
    position: relative;
}

.bottom-menu a:hover {
    text-decoration: underline;
}

.bottom-menu a.current {
    background: #2e4b83;
    color: #fff;
}


.b-search {
    float: right;

}

.b-search a {
    display: inline-block;
    text-decoration: none;
}

.b-search a:last-child {

    margin-left: 15px;
}

.b-primary {
    padding: 0 0 20px;
}

.b-sidebar {
    float: right;
    width: 280px;
    position: relative;
}

.b-search2 {
    background: #ffffff;
    padding: 10px 20px;
    color: #656565;
    border-radius: 10px;
    font-size: 12px;
    text-decoration: none;
    display: block;
    -moz-text-align-last: justify;
    text-align-last: justify;
    margin: 0 0 10px;
    font-weight: 500;
}

.b-search2 span {
    display: inline-block;
    vertical-align: middle;
}

.b-side {
    background: #fff;
    border-top: 5px solid #ec1c29;
    border-radius: 10px;
    margin-bottom: 10px;
    -webkit-box-shadow: 0 0 17px rgba(166, 181, 210, 0.29);
    box-shadow: 0 0 17px rgba(166, 181, 210, 0.29);
}

.title-block {
    text-transform: uppercase;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
    text-align: center;
    background: #f5f6f8;
    padding: 5px 0;
}


.b-post {
    position: relative;
    padding: 15px 10px 15px 0;
    border-bottom: 1px solid #d1d1d1;
    overflow: hidden;
}

.post-img {
    float: left;
    margin-right: 12px;
}

.post-img img {
    border-radius: 0 5px 5px 0;
}

.post-num {
    position: absolute;
    font-weight: 700;
    color: #fff;
    background: #8eaa2b;
    width: 13px;
    text-align: center;
    top: 20px;
    left: 0;
    font-size: 13px;
    line-height: 1.25;
}

.post-num:after {
    content: "";
    position: absolute;
    border-top: 8px solid #8eaa2b;
    border-bottom: 8px solid #8eaa2b;
    border-right: 5px solid transparent;
    top: 0;
    right: -5px;
}

.teaser {
    font-size: 16px;
    margin-bottom: 10px;
    color: #424242;
    line-height: 1;
}

.b-post:hover .teaser {
    color: #1b52bb;
}

.post-info {
    font-size: 11px;
    color: #a6a6a6;
    font-weight: 500;
}

.post-info span {
    display: inline-block;
}

.post-info span:last-child {
    float: right;
}

.post-link {
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}

.post-last {
    border: none;
}

.all-news {
    font-size: 12px;
    color: #fff;
    background: #2e4b83;
    text-align: center;
    text-transform: uppercase;
    display: block;
    text-decoration: none;
    padding: 5px;
    border-radius: 0 0 10px 10px;
}

.b-side:nth-child(3) {
    padding: 20px;
}

.left-social {
    margin-top: 20px;
}

.left-social a {
    display: inline-block;
    width: 37px;
    height: 37px;
    background: url("../images/7gWBFtbqjTxl.png") no-repeat;
    margin: 0 5px;
}

.left-social a.fb {
    background-position: 0 0;
}

.left-social a.tw {
    background-position: -117px 0;
}

.left-social a.in {
    background-position: -39px 0;
}

.left-social a.pn {
    background-position: -78px 0;
}

.bnr-block {
    position: relative;
    overflow: hidden;
}

.bnr-img {
    border-radius: 10px;
}

.bnr-text {
    position: absolute;
    top: 20px;
    font-size: 14px;
    color: #fff;
    line-height: 1;
    text-align: right;
    right: 10px;
}

.prod-name {
    font-size: 29px;
    font-weight: 900;
}

.prod-name+span {
    font-size: 12px;
    display: block;
}

.prod-name {
    font-weight: 300;
    font-size: 30px;
    display: block;
}

.bnr-img2 {
    position: absolute;
    right: -60px;
    bottom: -15px;
}

.btn {
    color: #fff;
    text-decoration: none;
    font-size: 15px;
    background: #0f0fba;
    display: inline-block;
    position: relative;
    padding: 8px 10px;
    -webkit-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    border: none;
    outline: none;
    cursor: pointer;
}

.btn:hover {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05);
}

.bnr-block .btn {
    margin-top: 10px;
}

.content .btn {
    font-size: 16px;
    font-weight: 700;
    padding: 24px 20px;
    border-radius: 35px;
    text-transform: uppercase;
}

.content {
    margin-right: 300px;
}

.main-content {
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    margin: 10px 0 35px;
    -webkit-box-shadow: 0 0 17px rgba(166, 181, 210, 0.29);
    box-shadow: 0 0 17px rgba(166, 181, 210, 0.29);
}

.b-tags {
    border-top: 3px solid #2e4b83;
    margin-bottom: 10px;
}

.b-tags>span {
    display: inline-block;
    color: #fff;
    background: #2e4b83;
    position: relative;
    font-weight: 700;
    font-size: 16px;
    padding: 5px;
}

.b-tags>span:after {
    content: "";
    position: absolute;
    border-top: 33px solid #2e4b83;
    border-right: 10px solid transparent;
    top: 0;
    right: -10px;
}


.breadcrumbs {
    float: right;
    font-size: 12px;
    margin: 10px 0 0;
}

.breadcrumbs a {
    text-decoration: none;
    color: #b7b7b7;
}

h2,
.h2 {
    font-size: 30px;
    line-height: 1;
    margin-bottom: 15px;
    font-weight: 700;
}

h1 {
    font-size: 48px;
    line-height: 1;
    font-weight: 500;
    margin-bottom: 5px;
}

.article-info {
    font-size: 11px;
    color: #a6a6a6;
    padding: 3px 0;
    border-top: 1px solid #ebebeb;
    border-bottom: 1px solid #ebebeb;
    margin-bottom: 20px;
}

.article-info>div {
    display: inline-block;
    vertical-align: middle;
}




.article-info span {
    display: inline-block;
    vertical-align: top;
    margin: 5px 20px 5px 0;
}

.simple-list {
    font-weight: 900;
    list-style-position: inside;
}

.simple-list li {
    margin-bottom: 10px;
}

.alignleft {
    float: left;
    margin: 0 25px 25px 0;
}

.separator {
    height: 0;
    width: 100%;
    border: none;
    border-bottom: 1px dashed #e2e2e2;
    margin: 30px 0;
}

.num-list {
    font-weight: 900;
    display: table;
    counter-reset: list1;
}

.num-list li {
    margin-bottom: 25px;
    list-style: none;
    padding-left: 20px;
    position: relative;
}

.num-list li:before {
    counter-increment: list1;
    content: '0'counter(list1);
    position: absolute;
    left: -40px;
    color: #fff;
    background: #ed1b28;
    border-radius: 50%;
    width: 44px;
    padding: 11px 0;
    text-align: center;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.b-stages {
    background: #f5f6f8;
    padding: 20px 10px 1px;
    margin-bottom: 20px;
}

.b-stages ol {
    list-style: none;
    counter-reset: list2;
}

.b-stages ol li {

    list-style: none;
    position: relative;
    margin-left: 45px;
    margin-bottom: 20px;
}

.b-stages ol li:before {
    counter-increment: list2;
    content: counter(list2);
    position: absolute;
    left: -55px;
    color: #fff;
    background: #2e4b83;
    font-size: 16px;
    font-weight: 700;
    width: 34px;
    padding: 7px 0;
    text-align: center;
    top: 3px;
}

.b-stages ol li:after {
    content: "";
    border-top: 35px solid #2e4b83;
    border-right: 10px solid transparent;
    position: absolute;
    top: 3px;
    left: -21px;
}

.img-wrap {

    margin-bottom: 15px;
    display: inline-block;
    background: #fff;
    padding: 10px;
    text-align: center;
}

.img-wrap img {
    margin: 10px 0;
    display: block;
}

.img-wrap span {

    width: 48%;
    display: inline-block;
}

.b-reviews {
    max-width: 520px;
    margin: 0 auto 15px;
    border: 1px solid #aab8c2;
    border-radius: 10px;
    padding: 25px 20px 0;
}

.b-review {
    padding-bottom: 20px;
    margin-bottom: 5px;
}

.review-ava {
    display: table-cell;
    padding-right: 10px;
    position: relative;
}

.review-ava img {
    border-radius: 5px;
}

.b-review:first-child .review-ava:after {
    content: "";
    width: 3px;
    height: calc(100% - 40px);
    top: 60px;
    background: #6eb0d3;
    left: 40%;
    position: absolute;
}

.review-body {
    display: table-cell;
    vertical-align: top;
}

.review-author {
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 5px;
    position: relative;
}

.review-author span {
    color: #aab8c2;
}

.review-author:after {
    width: 8px;
    height: 8px;
    content: "";
    border-right: 2px solid #abb9c2;
    border-bottom: 2px solid #abb9c2;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    position: absolute;
    top: 3px;
    right: 0;
}

.review-body img {
    border-radius: 5px;
}

.review-share {
    color: #aab8c2;
    font-size: 15px;
    font-weight: 700;
}

.review-share span {
    margin-right: 20px;
}

.comments-block .b-tags>span {
    font-size: 28px;
    margin-bottom: 10px;
    padding: 0 5px;
    line-height: 1.2;
}

.comments-block .b-tags .b-social {
    margin-top: 7px;
}

.comments-block .b-tags .b-social span {
    color: #bcbcbc;
    font-size: 11px;
    display: inline-block;
    vertical-align: middle;
}

.comments-block .b-tags .b-social a {
    display: inline-block;
    vertical-align: middle;
}

.comment-item {

    margin-bottom: 15px;
}

.comment-img,
.comment-body {
    display: table-cell;
    vertical-align: top;
}

.comment-img {
    padding-right: 15px;
}

.comment-author {
    color: #000;
    font-weight: 700;
    font-size: 16px;
    text-decoration: none;
    margin-right: 15px;
    margin-bottom: 10px;
    display: inline-block;
}


.comment-date {
    font-size: 13px;
    color: #adb2bf;
    margin-right: 10px;
}

.comment-like {
    font-size: 13px;
    color: #adb2bf;

}

.comment-body img {
    display: inline-block;
    vertical-align: top;
    margin-bottom: 10px;
}


@media (max-width: 990px) {
    .d-large {
        display: none !important;
    }

    .d-mobile {
        display: block !important;
    }

    .header-middle {
        background: #2e4b83;
        margin-top: 5px;
        padding: 0;
    }

    .b-logo {
        margin-left: 10px;
        background: #f5f6f8;
        padding: 0 14px 0 8px;
        position: relative;
        line-height: 0.8;
    }

    .b-logo:before {
        content: "";
        border-left: 14px solid #2e4b83;
        border-top: 11px solid transparent;
        border-bottom: 40px solid transparent;
        position: absolute;
        left: -2px;
        top: 0;
        -webkit-transform: rotate(5deg);
        -ms-transform: rotate(5deg);
        transform: rotate(5deg);
    }

    .b-logo:after {
        content: "";
        border-right: 14px solid #2e4b83;
        border-top: 50px solid transparent;
        position: absolute;
        right: -2px;
        top: 0;
        -webkit-transform: rotate(5deg);
        -ms-transform: rotate(5deg);
        transform: rotate(5deg);
    }

    .logo-red {
        font-size: 11px;
        vertical-align: top;
    }

    .logo-red:before,
    .logo-red:after {
        border-top-width: 12px;
    }

    .logo-blue {
        font-size: 20px;
        line-height: 1.1;
        /*margin: -3px 0 -5px 10px;*/
    }

    .logo-blue:before,
    .logo-blue:after {
        border-bottom-width: 22px;
    }

    .logo-black {
        font-size: 12px;
    }

    .logo-black:before,
    .logo-black:after {
        border-bottom-width: 16px;
    }

    .logo-white {
        font-size: 9px;
    }

    .b-search {
        margin: 12px 10px 0 0;
    }


    .content {
        margin-right: 0;
    }

    .content .b-tags {
        font-size: 12px;
        margin-bottom: 5px;
    }

    .main-content {

        margin-bottom: 30px;
    }

    .breadcrumbs {
        float: none;
        margin-bottom: 10px;
    }

    h2 {
        font-size: 24px;
        margin-bottom: 10px;
    }

    h1 {
        font-size: 38px;
        margin-bottom: 10px;
    }

    .separator {
        margin: 10px 0 15px;
    }



    .alignleft {
        float: none;
        display: block;
        margin: 0 auto 15px;
    }

    .num-list li {
        padding-left: 60px;
    }

    .num-list li:before {
        left: 0;
    }

    .b-stages ol li {
        margin-left: 30px;
    }

    .b-stages ol li:before {
        width: 22px;
        left: -40px;
    }

    .b-stages ol li:after {
        left: -18px;
    }

    .img-wrap {
        font-size: 14px;
        padding: 5px;
    }

    .img-wrap img {
        margin: 5px 0;
    }

    .b-reviews {
        padding: 15px 10px 0;
    }

    .comments-block {
        padding-left: 10px;
        padding-right: 10px;
    }

    .comments-block .b-tags>span {
        font-size: 24px;
        line-height: 1.4;
    }

    .comment-img,
    .comment-body {
        display: block;
    }

    .comment-img {
        float: left;
    }

    .comment-author {
        display: block;
    }

    .comment-body p {
        margin-top: 10px;
    }
}

@media (max-width: 430px) {
    .logo-blue {
        line-height: 22px;
        font-size: 13px;
        padding: 0;
    }

    .logo-white {
        height: 16px;
        line-height: 0.9;
        width: 140px;
        vertical-align: top;
    }
}

* {
    outline: none;
}

#order0 p {
    text-align: center;
    padding: 10px !important;
}

/* spin and pop-up  style*/

.close-popup {
    position: absolute;
    width: 30px;
    height: 30px;
    background-image: url('../images/YrpwSIBDyNif.svg');
    background-size: 100%;
    top: -40px;
    border-radius: 50%;
    -webkit-box-shadow: 0 0 10px #fff;
    box-shadow: 0 0 10px #fff;
    right: -40px;
    cursor: pointer;
}

.spin-result-wrapper {
    display: none;
    padding: 0 10px;
    width: 100%;
    top: 0;
    z-index: 999;
    left: 0;
    height: 100%;
    position: fixed;
    background-color: rgba(0, 0, 0, .6);
    text-align: center;
}

.pop-up-window {
    position: relative;
    max-width: 400px;
    right: 0px;
    left: 0px;
    top: 40%;
    margin: 0px auto;
    background: #ffffff none repeat scroll 0% 0%;
    text-align: center;
    padding: 10px;
    padding-top: 70px;
    padding-bottom: 20px;
    border-radius: 10px;
    animation: 0.7s ease 0s normal none 1 running pop-up-appear;
}

.pop-up-window::before {
    content: "";
    position: absolute;
    width: 110px;
    height: 110px;
    top: -55px;
    left: 0px;
    right: 0px;
    margin: 0px auto;
    background-color: rgb(113, 195, 65);
    border-radius: 50%;
    animation: 0.5s ease 0.6s normal backwards 1 running pop-up-appear-before;
}

.pop-up-window::after {
    content: "";
    position: absolute;
    width: 50px;
    height: 20px;
    top: -20px;
    left: 0px;
    right: 0px;
    margin: 0px auto;
    border-width: medium medium 4px 4px;
    border-style: none none solid solid;
    border-color: currentcolor currentcolor rgb(255, 255, 255) rgb(255, 255, 255);
    -moz-border-top-colors: none;
    -moz-border-right-colors: none;
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    border-image: none;
    transform: rotate(-45deg);
    transition: opacity 1s ease 0s;
    animation: 0.5s ease 0.6s normal backwards 1 running pop-up-appear-after;
}

@keyframes pop-up-appear {
    0% {
        transform: translateY(-2000px);
    }

    30% {
        transform: translateY(100px);
    }

    100% {
        transform: translateY(0px);
    }
}

@keyframes pop-up-appear-before {
    0% {
        transform: scale(0);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes pop-up-appear-after {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.pop-up-heading {
    font-size: 40px;
    margin-bottom: 20px;
}

.pop-up-text {
    margin-bottom: 25px;
    font-size: 24px;
    line-height: 30px;
    text-align: center;
}

.pop-up-button {
    text-transform: uppercase;
    text-decoration: none;
    padding: 10px 20%;
    font-size: 20px;
    border-radius: 5px;
    background-color: rgb(113, 195, 65);
    color: rgb(255, 255, 255);
    border: medium none;
    cursor: pointer;
    outline: medium none;
}

.pop-up-button:hover {
    color: rgb(255, 255, 255);
    text-decoration: none;
}

@media all and (max-width: 768px) {

    .pop-up-window {
        top: 25%;
    }

}

@media all and (max-width: 520px) {

    .pop-up-window {
        top: 25%;
        width: 85%;
        box-sizing: border-box;
        margin: 0 auto;
    }

    .close-popup {
        position: absolute;
        width: 30px;
        height: 30px;
        background-image: url('../images/YrpwSIBDyNif.svg');
        background-size: 100%;
        top: -40px;
        border-radius: 50%;
        -webkit-box-shadow: 0 0 10px #fff;
        box-shadow: 0 0 10px #fff;
        right: -10px;
        cursor: pointer;
    }

}

/* form */

#clock {
    color: red;
}

.order_block {
    display: block;
    text-align: center !important;
    margin-bottom: 20px;
}

.order_block img {
    max-width: 100%;
    margin: 20px auto;
}

.order_block h3 {
    font: bold 18px Arial !important;
    background: transparent;
}

.priceс {
    position: absolute;
    font-family: "Arial", sans-serif;
    right: 101px;
    bottom: -40px;
    color: #000;
    box-shadow: rgba(0, 0, 0, 0.8) 0px 3px 30px;
    width: 165px;
    height: 98px;
    text-align: center !important;
    font-size: 19px;
    font-weight: bold;
    line-height: 1.3;
    transform: rotateZ(-7deg);
    margin: -0px auto 0px;
    background: linear-gradient(to right, rgb(238, 165, 19) 0%, rgb(253, 237, 19) 100%);
    border-width: 2px;
    border-style: solid;
    border-color: rgb(255, 255, 255);
    border-image: initial;
    border-radius: 19%;
}

span.discountс {
    display: block;
    margin-top: 9px;
    font-size: 18px;
}

.price_main {
    border-bottom: 2px solid #d31812;
    font-size: 24px;
    line-height: 0px;
    margin: 0 auto;
}

.price_old {
    text-decoration: line-through;
}

.prod_img_wrapper {
    text-align: center;
    max-width: 420px;
    margin: 15px auto;
    position: relative;
    margin-bottom: 60px;
}

.prod_img {
    max-width: 100%;
}

.submit-roulette:hover {
    background: #bb0000;
    transition: background 0.3s;
}

.submit-roulette:active {
    transform: translateY(2px);
    box-shadow: none;
}

.submit-roulette {
    text-transform: uppercase;
    padding: 10px 15px;
    margin: 15px 0;
    outline: none;
    border: none;
    margin-top: 5px;
    border-radius: 5px;
    box-shadow: 0px 4px 3px #242120;
    background: red;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
    width: 100%;
    max-width: 260px;
}


.input-roulette {
    padding-left: 5px;
    height: 42px;
    margin: 0 auto 10px;
    width: 100%;
    max-width: 260px;
    font-size: 17px;
    box-sizing: border-box;
    display: block;
    border-radius: 5px;
    border: 1.5px solid #242120;
}

@media all and (max-width: 520px) {

    .prod_img_wrapper {
        margin-bottom: 50px;
    }
}

.x_country_select {display:none!important}
#order-in-progress__popup span {color: #000 !important;}