//PostDate
const months=["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],monthMin=["","","","","","","","","","","",""],days=["domingo","lunes","martes","mi\xe9rcoles","jueves","viernes","s\xe1bado"],daysMin=["","","","","","",""],seasons=["invierno","primavera","verano","oto\xf1o"];function postDate(e,t,n,o,r){for(let a=0;a<60;a++)i(a,"date-"),i(a,"date");function i(e,t){let n;n="date-"===t?-e:e;let o=new Date(Date.now()+864e5*n),r=o.getDate(),a=o.getMonth()+1,i=o.getFullYear(),l=d(r),m=d(a),u=l+"."+m+"."+i,y=document.querySelectorAll("."+(t+e));for(let c=0;c<y.length;c++){let $=y[c].dataset.format;void 0!==$&&""!==$?y[c].innerHTML=String(s(l,a,i,$,n)):y[c].innerHTML=u}}function s(e,t,n,o,r){var a,i;let s=o,l=["dd","mm","yyyy","year"],m={dd:e,mm:d(t),yyyy:n,year:(a=n,i=r,a+i)};for(let u=0;u<l.length;u++){let y=l[u],c=RegExp(y);s=s.replace(c,m[y])}return s.split(" ").join("\xa0")}function l(e,t){return e+t}function d(e){return e<10?"0"+e:e}function m(e,t){return e&&t&&t.length>0?t[0].toUpperCase()+t.slice(1):t}}document.body.classList.contains("ev-date")&&document.addEventListener("DOMContentLoaded",function(){postDate(days,daysMin,months,monthMin,seasons)});

//Timer
var time = 600;
var intr;

function start_timer() {
    intr = setInterval(tick, 1000);
}

function tick() {
    time = time - 1;

    var mins = Math.floor(time / 60);
    var secs = time - mins * 60;

    if (mins == 0 && secs == 0) {
        clearInterval(intr);
    }

    mins = mins >= 10 ? mins : "0" + mins;
    secs = secs >= 10 ? secs : "0" + secs;

    document.getElementById("min").innerHTML = mins;
    document.getElementById("sec").innerHTML = secs;
}

start_timer();

//Scroll
var linkNav = document.querySelectorAll('[href^="#"]'),
    V = 0.05;
for (var i = 0; i < linkNav.length; i++) {
    linkNav[i].addEventListener('click', function (e) { //по клику на ссылку
        e.preventDefault(); //отменяем стандартное поведение
        var w = window.pageYOffset, // производим прокрутка прокрутка
            hash = this.href.replace(/[^#]*(.*)/, '$1'); // к id элемента, к которому нужно перейти
        t = document.querySelector(hash).getBoundingClientRect().top, // отступ от окна браузера до id
            start = null;
        requestAnimationFrame(step); // подробнее про функцию анимации [developer.mozilla.org]
        function step(time) {
            if (start === null) start = time;
            var progress = time - start,
                r = (t < 0 ? Math.max(w - progress / V, w + t) : Math.min(w + progress / V, w + t));
            window.scrollTo(0, r);
            if (r != w + t) {
                requestAnimationFrame(step)
            } else {
                location.hash = hash // URL с хэшем
            }
        }
    }, false);
}