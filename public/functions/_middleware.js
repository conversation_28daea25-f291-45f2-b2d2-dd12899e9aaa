// Простой тестовый middleware для проверки работы
export async function onRequestGet(context) {
    const url = new URL(context.request.url);

    // Тестовый эндпоинт для проверки работы middleware
    if (url.pathname === "/test-middleware") {
        return new Response("✅ Middleware работает! Время: " + new Date().toISOString(), {
            status: 200,
            headers: { "Content-Type": "text/plain; charset=utf-8" }
        });
    }

    // Логируем все запросы для отладки
    const cfRay = context.request.headers.get('cf-ray') || `NO-RAY-${Date.now()}`;
    console.log(`[${cfRay}] 🔥 MIDDLEWARE TRIGGERED! Path: ${url.pathname}`);

    // Передаем управление основному middleware
    return mainMiddleware(context);
}

// --- НАЧАЛО КОНФИГУРАЦИИ ---
const OFFER_ID_LOG = "virex_all_one"; // Уникальный ID для логов этого проекта


const PATH_VARIANT_B = `/index_b.html`; // Локальный путь к варианту B

const TRUSTED_USER_VARIANTS_CONFIG = {
    enabled: true,
    identifierSource: 'ip_ua',
    variants: [
        { path: `/index_a1.html`,  weight: 34, name: "a1" }, // Локальные пути
        { path: `/index_a2.html`, weight: 33, name: "a2" }, // Локальные пути
        { path: `/index_a3.html`, weight: 33, name: "a3" }, // Локальные пути
    ]
};

const filterSettings = {
    FORCE_VARIANT_B: false, // Принудительно показывать вариант B
    // Добавьте другие настройки фильтров по необходимости
};
// --- КОНЕЦ КОНФИГУРАЦИИ ---

// --- Вспомогательные функции ---
async function getNumericHash(inputString) {
    const encoder = new TextEncoder();
    const data = encoder.encode(inputString);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    // Берем первые 8 символов и конвертируем в число
    return parseInt(hashHex.substring(0, 8), 16);
}

async function chooseWeightedSubVariantConfig(identifier, config) {
    if (!config.variants || config.variants.length === 0) {
        return null;
    }

    const hash = await getNumericHash(identifier);
    const totalWeight = config.variants.reduce((sum, variant) => sum + variant.weight, 0);
    const normalizedHash = hash % totalWeight;

    let currentWeight = 0;
    for (const variant of config.variants) {
        currentWeight += variant.weight;
        if (normalizedHash < currentWeight) {
            return variant;
        }
    }

    return config.variants[0]; // fallback
}

async function logVisit(env, data) {
    try {
        // Простое логирование в консоль, если база данных недоступна
        console.log(`[VISIT LOG] ${JSON.stringify(data)}`);

        // Попытка записи в D1 базу данных (если доступна)
        if (env && env.USER_TRACKING_DB) {
            await env.USER_TRACKING_DB.prepare(`
                INSERT INTO visits (timestamp, cf_ray, offer_id, variant_shown, country_code, client_ip, client_trust_category, as_organization, device_type, is_webview, webview_app_guess, os_name, browser_name, user_agent_raw, all_headers_raw, cf_object_raw, request_url, filter_passed_reason)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `).bind(
                data.timestamp, data.cfRay, data.offerId, data.variantShown,
                data.countryCode, data.clientIp, data.clientTrustCategory, data.asOrganization,
                data.deviceType, data.isWebview, data.webviewAppGuess, data.osName,
                data.browserName, data.userAgentRaw, data.allHeadersRaw, data.cfObjectRaw,
                data.requestUrl, data.filterPassedReason
            ).run();
        }
    } catch (error) {
        console.error('Error logging visit:', error);
    }
}

function parseUserAgent(ua) {
    // Упрощенный парсер User-Agent
    const result = {
        device: { type: 'desktop' },
        browser: { name: 'unknown', isWebview: false, webviewAppName: null },
        os: { name: 'unknown' }
    };

    if (!ua) return result;

    const uaLower = ua.toLowerCase();

    // Определение устройства
    if (uaLower.includes('mobile') || uaLower.includes('android')) {
        result.device.type = 'mobile';
    } else if (uaLower.includes('tablet') || uaLower.includes('ipad')) {
        result.device.type = 'tablet';
    }

    // Определение браузера
    if (uaLower.includes('chrome')) result.browser.name = 'chrome';
    else if (uaLower.includes('firefox')) result.browser.name = 'firefox';
    else if (uaLower.includes('safari')) result.browser.name = 'safari';
    else if (uaLower.includes('edge')) result.browser.name = 'edge';

    // Определение ОС
    if (uaLower.includes('windows')) result.os.name = 'windows';
    else if (uaLower.includes('mac')) result.os.name = 'macos';
    else if (uaLower.includes('linux')) result.os.name = 'linux';
    else if (uaLower.includes('android')) result.os.name = 'android';
    else if (uaLower.includes('ios')) result.os.name = 'ios';

    // Проверка на webview
    if (uaLower.includes('wv') || uaLower.includes('webview')) {
        result.browser.isWebview = true;
    }

    return result;
}
// --- Конец вспомогательных функций ---


async function mainMiddleware(context) {
    const { request, env, waitUntil, next, rewrite } = context;

    const url = new URL(request.url);
    const path = url.pathname;

    // Определяем пути, которые будут обрабатываться нашей логикой A/B
    const baseAbTestPaths = ["/", "/index", "/index.html"]; // Основные точки входа
    // Добавляем пути из конфига A/B и путь для варианта B
    const dynamicAbTestPaths = TRUSTED_USER_VARIANTS_CONFIG.variants.map(v => v.path.replace('.html', '')); // /index_a1
    const dynamicAbTestPathsHtml = TRUSTED_USER_VARIANTS_CONFIG.variants.map(v => v.path); // /index_a1.html
    const pathVariantBNoHtml = PATH_VARIANT_B.replace('.html', ''); // /index_b

    const allTriggerPaths = [...new Set([
        ...baseAbTestPaths,
        ...dynamicAbTestPaths,
        ...dynamicAbTestPathsHtml,
        PATH_VARIANT_B,
        pathVariantBNoHtml
    ])];


    // Проверяем, является ли текущий путь одним из тех, для которых нужна A/B логика
    // или если это корневой путь.
    // Для CSS, JS, изображений и т.д., мы хотим пропустить эту логику.
    const isAssetRequest = /\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i.test(path);

    if (!isAssetRequest && (allTriggerPaths.includes(path) || path === "/" || path === "/index.html" || path === "/index")) {
        const timestamp = new Date().toISOString();
        const cfRay = request.headers.get('cf-ray') || `no-ray-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        // ... (вся ваша логика сбора данных: cfData, clientIp, userAgentRaw и т.д. остается как есть) ...
        const cfData = request.cf || {};
        const countryCode = cfData.country || null;
        const clientIp = request.headers.get('CF-Connecting-IP') || "UNKNOWN_IP";
        const asOrganization = cfData.asOrganization || "Unknown AS Organization";
        const clientTrustScore = cfData.clientTrustScore || null;
        const userAgentRaw = request.headers.get('User-Agent') || "";
        const headersObject = {};
        for (const [key, value] of request.headers) { headersObject[key] = value; }
        const allHeadersRaw = JSON.stringify(headersObject);
        const cfObjectRaw = JSON.stringify(cfData);
        const uaInfo = parseUserAgent(userAgentRaw);
        let clientTrustCategory = "unknown";
        if (clientTrustScore !== null) {
            if (clientTrustScore < 15) clientTrustCategory = "very_low_trust";
            else if (clientTrustScore < 50) clientTrustCategory = "low_trust";
            else clientTrustCategory = "high_trust";
        }

        let targetVariantPath;
        let filterPassedReason = "initial";
        let variantDecisionForLog;

        // --- Ваша логика фильтрации и выбора варианта (остается как в вашем Worker'е) ---
        // ... (вставьте сюда вашу логику if (filterSettings.FORCE_VARIANT_B) { ... } else { ... } )
        // Убедитесь, что targetVariantPath это локальный путь, например, "/index_a1.html"
        // Пример из вашего кода:
        if (filterSettings.FORCE_VARIANT_B) {
            targetVariantPath = PATH_VARIANT_B;
            filterPassedReason = "forced_variant_b";
            variantDecisionForLog = "b";
        } else {
            let reasonForB = null;
            // ... (ваши проверки фильтров) ...
            if (reasonForB) {
                targetVariantPath = PATH_VARIANT_B;
                filterPassedReason = reasonForB;
                variantDecisionForLog = "b";
            } else {
                filterPassedReason = "passed_all_filters";
                let chosenTrustedVariant = null;
                if (TRUSTED_USER_VARIANTS_CONFIG.enabled && TRUSTED_USER_VARIANTS_CONFIG.variants && TRUSTED_USER_VARIANTS_CONFIG.variants.length > 0) {
                    let identifier = (TRUSTED_USER_VARIANTS_CONFIG.identifierSource === 'ip_ua')
                        ? (clientIp + userAgentRaw)
                        : clientIp;
                    chosenTrustedVariant = await chooseWeightedSubVariantConfig(identifier, TRUSTED_USER_VARIANTS_CONFIG);
                }

                if (chosenTrustedVariant && chosenTrustedVariant.path) {
                    targetVariantPath = chosenTrustedVariant.path; // Это уже локальный путь типа "/index_a1.html"
                    variantDecisionForLog = chosenTrustedVariant.name || chosenTrustedVariant.path.substring(1).replace('.html', '');
                    filterPassedReason += `_to_split_${variantDecisionForLog}`;
                } else {
                    // Fallback, если что-то пошло не так с выбором доверенного варианта
                    console.error(`[${cfRay}] CRITICAL: Trusted user, but no sub-variant chosen. Fallback to B. Check TRUSTED_USER_VARIANTS_CONFIG.`);
                    targetVariantPath = PATH_VARIANT_B;
                    filterPassedReason = "trusted_user_no_subvariant_fallback_to_b";
                    variantDecisionForLog = "b_fallback_trusted";
                }
            }
        }
        // --- Конец вашей логики фильтрации ---

        // Логирование визита (асинхронно)
        waitUntil(
            logVisit(env, { // env здесь из context.env
                timestamp, cfRay, offerId: OFFER_ID_LOG,
                variantShown: variantDecisionForLog,
                countryCode, clientIp, clientTrustCategory, asOrganization,
                deviceType: uaInfo.device.type, isWebview: uaInfo.browser.isWebview,
                webviewAppGuess: uaInfo.browser.webviewAppName, osName: uaInfo.os.name,
                browserName: uaInfo.browser.name, userAgentRaw, allHeadersRaw,
                cfObjectRaw, requestUrl: request.url, filterPassedReason
            })
        );

        // Вместо fetch на внешний URL, мы "переписываем" запрос так,
        // чтобы Cloudflare Pages отдал соответствующий локальный HTML файл.
        // `rewrite` немедленно возвращает Response, который отдает указанный файл.
        console.log(`[${cfRay}] Rewriting to: ${targetVariantPath}`);
        return rewrite(targetVariantPath); // Например, rewrite("/index_a1.html")

    } else {
        // Если это запрос на статический ассет (CSS, JS, image) или путь не требует A/B,
        // передаем его дальше для стандартной обработки Cloudflare Pages.
        // `next()` вызовет стандартный обработчик статических файлов Pages.
        // console.log(`[${request.headers.get('cf-ray') || 'NO-RAY'}] Path ${path} is an asset or not in AB paths. Passing to next static handler.`);
        return await next();
    }
}