// timer

var sec = document.querySelector("#sec");
var min = document.querySelector("#min");

var secund = 60;
var minute = 10;

sec.innerText = --secund;
min.innerText = --minute;

setInterval(function () {
  if (secund <= 1) secund = 60;
  sec.innerText = --secund;
}, 1000);
setInterval(function () {
  if (minute <= 1) minute = 10;
  min.innerText = --minute;
}, 60000);

// comments-items
var $data_now = "Hace un momento";
var $like = "Me gusta";
var $answer = "Responder";
var $yet = "Más";
var $answered = "respondió";
var $hours = "r.";

function respues(e) {
  if (e > 1) return "respuestas";
  else return "respuesta";
}

("use strict");

function _createForOfIteratorHelperLoose(e) {
  var t = 0;
  if ("undefined" != typeof Symbol && null != e[Symbol.iterator]) return (t = e[Symbol.iterator]()).next.bind(t);
  if (Array.isArray(e) || (e = _unsupportedIterableToArray(e)))
    return function () {
      return t >= e.length
        ? {
            done: !0,
          }
        : {
            done: !1,
            value: e[t++],
          };
    };
  throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

function _unsupportedIterableToArray(e, t) {
  if (e) {
    if ("string" == typeof e) return _arrayLikeToArray(e, t);
    var o = Object.prototype.toString.call(e).slice(8, -1);
    return (
      "Object" === o && e.constructor && (o = e.constructor.name),
      "Map" === o || "Set" === o ? Array.from(o) : "Arguments" === o || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o) ? _arrayLikeToArray(e, t) : void 0
    );
  }
}

function _arrayLikeToArray(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var o = 0, r = new Array(t); o < t; o++) r[o] = e[o];
  return r;
}

function _typeof(e) {
  return (_typeof =
    "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
      ? function (e) {
          return typeof e;
        }
      : function (e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e;
        })(e);
}
document.addEventListener("DOMContentLoaded", function () {
  function e() {
    if (!("scrollBehavior" in m.documentElement.style)) {
      var e = f.HTMLElement || f.Element,
        c = 468,
        i = {
          scroll: f.scroll || f.scrollTo,
          scrollBy: f.scrollBy,
          elScroll: e.prototype.scroll || u,
          scrollIntoView: e.prototype.scrollIntoView,
        },
        s = f.performance && f.performance.now ? f.performance.now.bind(f.performance) : Date.now;
      (f.scroll = f.scrollTo =
        function () {
          r(arguments[0])
            ? i.scroll.call(f, arguments[0].left || arguments[0], arguments[0].top || arguments[1])
            : n.call(f, m.body, ~~arguments[0].left, ~~arguments[0].top);
        }),
        (f.scrollBy = function () {
          r(arguments[0])
            ? i.scrollBy.call(f, arguments[0].left || arguments[0], arguments[0].top || arguments[1])
            : n.call(f, m.body, ~~arguments[0].left + (f.scrollX || f.pageXOffset), ~~arguments[0].top + (f.scrollY || f.pageYOffset));
        }),
        (e.prototype.scroll = e.prototype.scrollTo =
          function () {
            r(arguments[0])
              ? i.elScroll.call(this, arguments[0].left || arguments[0], arguments[0].top || arguments[1])
              : n.call(this, this, arguments[0].left, arguments[0].top);
          }),
        (e.prototype.scrollBy = function () {
          var e = arguments[0];
          "object" === _typeof(e)
            ? this.scroll({
                left: e.left + this.scrollLeft,
                top: e.top + this.scrollTop,
                behavior: e.behavior,
              })
            : this.scroll(this.scrollLeft + e, this.scrollTop + arguments[1]);
        }),
        (e.prototype.scrollIntoView = function () {
          if (r(arguments[0])) i.scrollIntoView.call(this, arguments[0] || !0);
          else {
            var e = (function (e) {
                for (
                  var t, o, r;
                  (t = (e = e.parentNode) === m.body),
                    (o = e.clientHeight < e.scrollHeight || e.clientWidth < e.scrollWidth),
                    (r = "visible" === f.getComputedStyle(e, null).overflow),
                    !t && (!o || r);

                );
                return (t = o = r = null), e;
              })(this),
              t = e.getBoundingClientRect(),
              o = this.getBoundingClientRect();
            e !== m.body
              ? (n.call(this, e, e.scrollLeft + o.left - t.left, e.scrollTop + o.top - t.top),
                f.scrollBy({
                  left: t.left,
                  top: t.top,
                  behavior: "smooth",
                }))
              : f.scrollBy({
                  left: o.left,
                  top: o.top,
                  behavior: "smooth",
                });
          }
        });
    }

    function u(e, t) {
      (this.scrollLeft = e), (this.scrollTop = t);
    }

    function r(e) {
      if ("object" !== _typeof(e) || null === e || void 0 === e.behavior || "auto" === e.behavior || "instant" === e.behavior) return 1;
      if ("object" !== _typeof(e) || "smooth" !== e.behavior) throw new TypeError("behavior not valid");
    }

    function p(e) {
      var t,
        o,
        r,
        n,
        l = (s() - e.startTime) / c;
      (n = l = 1 < l ? 1 : l),
        (t = 0.5 * (1 - Math.cos(Math.PI * n))),
        (o = e.startX + (e.x - e.startX) * t),
        (r = e.startY + (e.y - e.startY) * t),
        e.method.call(e.scrollable, o, r),
        (o === e.x && r === e.y) || f.requestAnimationFrame(p.bind(f, e));
    }

    function n(e, t, o) {
      var r,
        n,
        l,
        c,
        a = s();
      (c =
        e === m.body ? ((n = (r = f).scrollX || f.pageXOffset), (l = f.scrollY || f.pageYOffset), i.scroll) : ((n = (r = e).scrollLeft), (l = e.scrollTop), u)),
        p({
          scrollable: r,
          method: c,
          startTime: a,
          startX: n,
          startY: l,
          x: t,
          y: o,
        });
    }
  }
  var f, m;
  (f = window),
    (m = document),
    "object" === ("undefined" == typeof exports ? "undefined" : _typeof(exports))
      ? (module.exports = {
          polyfill: e,
        })
      : e(),
    document.querySelectorAll(".component_reposy b").forEach(function (e) {
      return (e.innerText = $data_now);
    }),
    document.querySelectorAll(".component_reposy nav:nth-child(2)").forEach(function (e) {
      return (e.innerText = $like);
    }),
    document.querySelectorAll(".component_reposy nav:nth-child(3)").forEach(function (e) {
      return (e.innerText = $answer);
    }),
    document.querySelectorAll(".component_reposy nav:nth-child(4)").forEach(function (e) {
      return (e.innerText = $yet);
    });
  for (
    var t,
      o = document.querySelectorAll(".item:not(oneq) .component_name, .item .component_name, .item .component_reposy"),
      r = _createForOfIteratorHelperLoose(o);
    !(t = r()).done;

  ) {
    t.value.addEventListener("click", function (e) {
      e.preventDefault();
      document.getElementById("form-wrap").scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    });
  }

  function c() {
    if ("undefined" != typeof pageYOffset) return pageYOffset;
    var e = document.body,
      t = document.documentElement;
    return (t = t.clientHeight ? t : e).scrollTop;
  }
  var a = document.querySelector("#popup");
  var i = !0;
  document.addEventListener("scroll", function () {
  }),
    (comments = document.querySelectorAll("#comments_component .item")),
    (commentsArr = []),
    (hours_num = 1),
    comments.forEach(function (e, t) {
      var o = Math.floor(Math.random() * Math.floor(3));
      4 < t ? (e.querySelector(".component_reposy b").innerText = hours_num + " " + $hours) : (hours_num = 1),
        (hours_num += o),
        commentsArr.push({
          elem: e,
        });
    });
  var s = Math.random().toString(36).substr(2),
    u = [],
    p = 0;
  commentsArr.filter(function (e) {
    "item user_request" === e.elem.getAttribute("class")
      ? (e.elem.setAttribute("hash", s), (u[p] = e.elem))
      : ((s = Math.random().toString(36).substr(2)), p++);
  }),
    u.forEach(function (t) {
      t.classList.add("oneq");
      var o = t.getAttribute("hash"),
        e = commentsArr.filter(function (e) {
          return e.elem.getAttribute("hash") === o;
        }).length;
      (saveYextName = t.querySelector(".component_name").innerHTML),
        (t.querySelector(".component_name").innerHTML +=
          '<span style="font-weight:100;margin-left:5px!important;color:#000">' +
          $answered +
          ' <b style="font-size: 13px;position: relative;top: -2.5px;">.</b><b style="font-weight:100;color:gray">' +
          e +
          '</b></span><span style="font-weight:100;color:gray"> ' +
          respues(e) +
          "</span>"),
        (elems = commentsArr.filter(function (e) {
          return e.elem.getAttribute("hash") === o;
        })),
        elems.forEach(function (e) {
          return (e.elem.style.display = "none");
        }),
        t.addEventListener("click", function (e) {
          (t.querySelector(".component_name").innerHTML = t.querySelector(".component_name").innerHTML),
            t.querySelector(".component_name span").remove(),
            t.querySelector(".component_name span").remove(),
            t.classList.remove("oneq"),
            commentsArr
              .filter(function (e) {
                return e.elem.getAttribute("hash") === o;
              })
              .forEach(function (e) {
                return (e.elem.style.display = "flex");
              });
        });
    }),
    (isLiced = function (e) {
      return (
        (icon_l = '<span class="fb_licked l"></span>'),
        (icon_u = '<span class="fb_licked u"></span>'),
        (icon_s = '<span class="fb_licked s"></span>'),
        ($popular = ""),
        ($dispaly = "flex"),
        e <= 0 && ($dispaly = "none"),
        0 <= e && ($popular = icon_l),
        15 <= e && ($popular = icon_u),
        30 <= e && ($popular = icon_s),
        50 <= e && ($popular = icon_l + icon_u),
        65 <= e && ($popular = icon_l + icon_s),
        80 <= e && ($popular = icon_u + icon_s),
        100 <= e && ($popular = icon_l + icon_u + icon_s),
        {
          popular: $popular,
          dispaly: $dispaly,
        }
      );
    }),
    document.querySelectorAll(".component_licked").forEach(function (e) {
      var t = Math.floor(Math.random() * Math.floor(130));
      t <= 80
        ? (t = 0)
        : (($n = Math.floor(Math.random() * Math.floor(130))),
          (e.querySelector(".icons").innerHTML = isLiced($n).popular),
          (e.querySelector(".popular").innerHTML = $n),
          (e.style.display = "flex"));
    });
});

// scroll
var linkNav = document.querySelectorAll('[href^="#"]'),
  V = 0.1;
for (var i = 0; i < linkNav.length; i++) {
  linkNav[i].addEventListener(
    "click",
    function (e) {
      //по клику на ссылку
      e.preventDefault(); //отменяем стандартное поведение
      var w = window.pageYOffset, // производим прокрутка прокрутка
        hash = this.href.replace(/[^#]*(.*)/, "$1"); // к id элемента, к которому нужно перейти
      (t = document.querySelector(hash).getBoundingClientRect().top + 20), // отступ от окна браузера до id
        (start = null);
      requestAnimationFrame(step); // подробнее про функцию анимации [developer.mozilla.org]
      function step(time) {
        if (start === null) start = time;
        var progress = time - start,
          r = t < 0 ? Math.max(w - progress / V, w + t) : Math.min(w + progress / V, w + t);
        window.scrollTo(0, r);
        if (r != w + t) {
          requestAnimationFrame(step);
        } else {
          location.hash = hash; // URL с хэшем
        }
      }
    },
    false
  );
}

const months = ["enero", "febrero", "marzo", "abril", "mayo", "junio", "julio", "agosto", "septiembre", "octubre", "noviembre", "diciembre"],
  monthMin = ["", "", "", "", "", "", "", "", "", "", "", ""],
  days = ["domingo", "lunes", "martes", "miércoles", "jueves", "viernes", "sábado"],
  daysMin = ["", "", "", "", "", "", ""],
  seasons = ["invierno", "primavera", "verano", "otoño"];
function postDate(daysName, daysMinName, monthsName, monthsMinName, seasonsName) {
  const _counterLength = 60;
  for (let counter = 0; counter < _counterLength; counter++) {
    innerDate(counter, "date-");
    innerDate(counter, "date");
  }
  function innerDate(counter, dateType) {
    let newCounter;
    dateType === "date-" ? (newCounter = -counter) : (newCounter = counter);
    const _msInDay = 86400000,
      _localDate = new Date(Date.now() + newCounter * _msInDay),
      _day = _localDate.getDate(),
      _month = _localDate.getMonth() + 1,
      _year = _localDate.getFullYear();
    const dayDefault = addZero(_day),
      monthDefault = addZero(_month),
      defaultDate = dayDefault + "." + monthDefault + "." + _year;
    const dateClass = dateType + counter,
      nodeList = document.querySelectorAll("." + dateClass);
    for (let i = 0; i < nodeList.length; i++) {
      const dateFormat = nodeList[i].dataset.format;
      dateFormat !== undefined && dateFormat !== ""
        ? (nodeList[i].innerHTML = String(changeFormat(dayDefault, _month, _year, dateFormat, newCounter)))
        : (nodeList[i].innerHTML = defaultDate);
    }
  }
  function changeFormat(_day, _month, _year, format, counter) {
    let innerFormat = format;
    const testFormat = ["dd", "mm", "yyyy", "monthFull", "year"],
      dateFormat = { dd: _day, mm: addZero(_month), yyyy: _year, monthFull: getMonthName(_month, monthsName, false), year: getYearWithCounter(_year, counter) };
    for (let i = 0; i < testFormat.length; i++) {
      let string = testFormat[i];
      let regExp = new RegExp(string);
      innerFormat = innerFormat.replace(regExp, dateFormat[string]);
    }
    return innerFormat.split(" ").join(" ");
  }
  function getMonthName(_month, monthsName, bigFirstLetter, counter) {
    const monthCounter = !!counter ? counter : 0;
    let month;
    _month + monthCounter > 12 ? (month = monthCounter - (12 - _month)) : (month = _month + monthCounter);
    _month + monthCounter <= 0 ? (month = 12 + monthCounter + 1) : (month = _month + monthCounter);
    return changeFirstLetter(bigFirstLetter, monthsName[month - 1]);
  }
  function getYearWithCounter(year, counter) {
    return year + counter;
  }
  function addZero(numb) {
    return numb < 10 ? "0" + numb : numb;
  }
  function changeFirstLetter(isBig, str) {
    return isBig && str && str.length > 0 ? str[0].toUpperCase() + str.slice(1) : str;
  }
}
if (document.body.classList.contains("ev-date")) {
  document.addEventListener("DOMContentLoaded", function () {
    postDate(days, daysMin, months, monthMin, seasons);
  });
}

function openImg(e) {
  const target = e.target;
  if (target.classList.contains("first") || target.classList.contains("second")) {
    const parent = target.parentNode.parentNode;
    parent.classList.toggle("show");
  }
}

document.addEventListener("click", openImg);
